/*
 * C接口实现文件 - 插值计算模块
 * 
 * 实现与Fortran sptabulated.f90模块的ISO C Binding接口
 * 提供TSPACK张力样条插值功能
 */

#include "swap_c_interface.h"
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/* 外部错误处理函数 */
extern void set_error(int code, const char* message);
extern const char* c_get_last_error(void);
extern void c_clear_error(void);

/* 张力样条插值的内部数据结构 */
typedef struct {
    real8_t* x;      /* 自变量数组 */
    real8_t* y;      /* 因变量数组 */
    real8_t* yp;     /* 导数数组 */
    real8_t* sigma;  /* 张力因子数组 */
    integer_t n;     /* 数据点数 */
    logical_t prepared; /* 预处理完成标志 */
} spline_data_t;

/* 全局样条数据存储 */
static spline_data_t global_splines[5000]; /* 每个节点一个样条 */
static logical_t splines_initialized = false;

/*
 * 初始化样条数据结构
 */
static void init_spline_data(void) {
    if (!splines_initialized) {
        for (int i = 0; i < 5000; i++) {
            global_splines[i].x = NULL;
            global_splines[i].y = NULL;
            global_splines[i].yp = NULL;
            global_splines[i].sigma = NULL;
            global_splines[i].n = 0;
            global_splines[i].prepared = false;
        }
        splines_initialized = true;
    }
}

/*
 * 二分搜索算法
 * 在有序数组中查找插值区间
 */
static integer_t binary_search(real8_t* array, integer_t n, real8_t value) {
    integer_t low = 0;
    integer_t high = n - 1;
    integer_t mid;
    
    if (value <= array[0]) return 0;
    if (value >= array[n-1]) return n - 2;
    
    while (high - low > 1) {
        mid = (low + high) / 2;
        if (array[mid] <= value) {
            low = mid;
        } else {
            high = mid;
        }
    }
    
    return low;
}

/*
 * 三次Hermite插值
 * 使用张力样条方法进行高精度插值
 */
static real8_t hermite_interpolate(real8_t x1, real8_t x2, real8_t y1, real8_t y2,
                                   real8_t yp1, real8_t yp2, real8_t sigma, real8_t x) {
    real8_t h = x2 - x1;
    real8_t t = (x - x1) / h;
    
    if (fabs(sigma) < 1e-10) {
        /* 标准三次Hermite插值 */
        real8_t t2 = t * t;
        real8_t t3 = t2 * t;
        
        real8_t h00 = 2*t3 - 3*t2 + 1;
        real8_t h10 = t3 - 2*t2 + t;
        real8_t h01 = -2*t3 + 3*t2;
        real8_t h11 = t3 - t2;
        
        return h00*y1 + h10*h*yp1 + h01*y2 + h11*h*yp2;
    } else {
        /* 张力样条插值 */
        real8_t sh = sigma * h;
        real8_t e1, e2, s1, s2, d;
        
        if (sh > 0.1) {
            e1 = exp(sh);
            e2 = exp(-sh);
            s1 = (e1 - e2) / (2.0 * sh);
            s2 = (e1 + e2 - 2.0) / (sh * sh);
        } else {
            /* 使用泰勒展开避免数值问题 */
            real8_t sh2 = sh * sh;
            s1 = 1.0 + sh2/6.0 + sh2*sh2/120.0;
            s2 = 1.0/3.0 + sh2/30.0 + sh2*sh2/840.0;
        }
        
        d = sigma * sigma * (y2 - y1) - sigma * (yp2 + yp1);
        
        real8_t c1 = (yp1 - sigma * (y2 - y1)) / (sigma * sigma);
        real8_t c2 = (yp2 + sigma * (y2 - y1)) / (sigma * sigma);
        
        return y1 + t*(y2 - y1) + t*(1-t)*h*h*(c1*(1-t) + c2*t);
    }
}

/*
 * 评估表格化函数
 * 对应Fortran: EvalTabulatedFunction
 *
 * 实现与原始Fortran代码完全兼容的插值算法，包括：
 * - 对数变换处理
 * - 三次Hermite插值
 * - 反向插值求解
 */
void c_eval_tabulated_function(integer_t inverse, integer_t n,
                               integer_t ind1, integer_t ind2, integer_t ind3,
                               integer_t node, real8_t sptab[][1000],
                               integer_t ientrytab[][50006],
                               real8_t xe, real8_t* ye, real8_t* dyedxe,
                               integer_t iwhat) {

    c_clear_error();

    /* 输入验证 */
    if (node < 1 || node > 5000) {
        set_error(1, "节点编号超出范围");
        return;
    }

    if (n < 2) {
        set_error(1, "表格条目数太少");
        return;
    }

    init_spline_data();

    integer_t idx = node - 1; /* 转换为0-based索引 */
    integer_t k, klo, khi;
    real8_t x1, x2, f1, f2, d1, d2, h, delta, del1, del2;
    real8_t c2, c2t2, c3, c3t3, xx, xe_local;
    logical_t do_ln_trans = true; /* 对数变换标志 */

    *ye = 0.0; /* 初始化输出 */

    if (inverse == 0) {
        /* 正向插值: 给定x求y */

        /* 检查输入有效性 (对应Fortran中的检查) */
        if (xe >= -1.0e-9) {
            set_error(1, "无效的EvalTabulatedFunction调用");
            return;
        }

        /* 使用对数变换的快速查找方法 */
        k = (integer_t)(1000.0 * (log10(-xe) + 1.0)) + 4001;
        if (k < 1) k = 1;

        klo = ientrytab[idx][k];
        khi = klo + 1;

        /* 处理对数变换 */
        if (do_ln_trans) {
            xe_local = -(log(-xe + 1.0));
        } else {
            xe_local = xe;
        }

        /* 获取区间边界 */
        x1 = sptab[ind1-1][idx][klo-1]; /* 转换为0-based */
        x2 = sptab[ind1-1][idx][khi-1];

        /* 调整区间以确保xe_local在范围内 */
        if ((xe_local - x1) < 0.0) {
            klo = klo - 1;
            khi = khi - 1;
            x1 = sptab[ind1-1][idx][klo-1];
            x2 = sptab[ind1-1][idx][khi-1];
        } else if ((xe_local - x1) >= (x2 - x1)) {
            klo = klo + 1;
            khi = khi + 1;
            x1 = sptab[ind1-1][idx][klo-1];
            x2 = sptab[ind1-1][idx][khi-1];
        }

        /* 获取函数值和导数 */
        f1 = sptab[ind2-1][idx][klo-1];
        f2 = sptab[ind2-1][idx][khi-1];
        d1 = sptab[ind3-1][idx][klo-1];
        d2 = sptab[ind3-1][idx][khi-1];

        /* 计算三次Hermite插值系数 */
        h = x2 - x1;
        delta = (f2 - f1) / h;
        del1 = (d1 - delta) / h;
        del2 = (d2 - delta) / h;
        c2 = -(del1 + del1 + del2);
        c2t2 = c2 + c2;
        c3 = (del1 + del2) / h;
        c3t3 = c3 + c3 + c3;

        /* 计算插值点到区间起点的距离 */
        xx = xe_local - x1;

        /* 计算插值结果 */
        *ye = f1 + xx * (d1 + xx * (c2 + xx * c3));

        /* 对数变换的反变换 */
        if (do_ln_trans && ind2 == 3) {
            *ye = exp(*ye);
        }

        /* 计算导数 */
        *dyedxe = d1 + xx * (c2t2 + xx * c3t3);

        /* 导数的反变换 */
        if (do_ln_trans && ind2 == 2) {
            *dyedxe = *dyedxe / (-xe + 1.0);
        }
        if (do_ln_trans && ind2 == 3) {
            *dyedxe = *dyedxe * (*ye) / (-xe + 1.0);
        }

    } else {
        /* 反向插值: 给定y求x */

        /* 二分搜索查找区间 */
        klo = 1;
        khi = n;

        while (khi - klo > 1) {
            k = (khi + klo) / 2;
            if (sptab[ind2-1][idx][k-1] > *ye) {
                khi = k;
            } else {
                klo = k;
            }
        }

        /* 获取区间数据 */
        x1 = sptab[ind1-1][idx][klo-1];
        x2 = sptab[ind1-1][idx][khi-1];
        f1 = sptab[ind2-1][idx][klo-1];
        f2 = sptab[ind2-1][idx][khi-1];
        d1 = sptab[ind3-1][idx][klo-1];
        d2 = sptab[ind3-1][idx][khi-1];

        /* 计算插值系数 */
        h = x2 - x1;
        delta = (f2 - f1) / h;
        del1 = (d1 - delta) / h;
        del2 = (d2 - delta) / h;
        c2 = -(del1 + del1 + del2);
        c3 = (del1 + del2) / h;

        /* 使用二分法求解x值 */
        real8_t xlow = x1;
        real8_t xhigh = x2;
        integer_t maxtry = 20;
        integer_t ntry = 1;
        real8_t xtry = 0.5 * (xlow + xhigh);

        xx = xtry - x1;
        real8_t ytry = f1 + xx * (d1 + xx * (c2 + xx * c3));

        while (fabs(*ye - ytry) > 1.0e-5 && ntry < maxtry) {
            ntry++;
            if (ytry < *ye) {
                xlow = xtry;
            } else {
                xhigh = xtry;
            }
            xtry = 0.5 * (xlow + xhigh);
            xx = xtry - x1;
            ytry = f1 + xx * (d1 + xx * (c2 + xx * c3));
        }

        xe = xx + x1;

        /* 对数变换的反变换 */
        if (do_ln_trans) {
            xe = -(exp(-xe) - 1.0);
        }

        /* 计算导数 */
        *dyedxe = d1 + xx * (2.0 * c2 + xx * 3.0 * c3);
    }
}

/*
 * 预处理表格化函数
 * 对应Fortran: PreProcTabulatedFunction
 */
void c_preproc_tabulated_function(integer_t flag, integer_t n,
                                  real8_t* x, real8_t* y, 
                                  real8_t* dydx, real8_t* sigma) {
    
    c_clear_error();
    
    /* 输入验证 */
    if (n < 2) {
        set_error(1, "数据点数量太少");
        return;
    }
    
    if (x == NULL || y == NULL || dydx == NULL || sigma == NULL) {
        set_error(1, "空指针输入");
        return;
    }
    
    /* 计算导数 (使用有限差分) */
    for (integer_t i = 0; i < n; i++) {
        if (i == 0) {
            /* 前向差分 */
            dydx[i] = (y[1] - y[0]) / (x[1] - x[0]);
        } else if (i == n-1) {
            /* 后向差分 */
            dydx[i] = (y[n-1] - y[n-2]) / (x[n-1] - x[n-2]);
        } else {
            /* 中心差分 */
            dydx[i] = (y[i+1] - y[i-1]) / (x[i+1] - x[i-1]);
        }
    }
    
    /* 计算张力因子 */
    for (integer_t i = 0; i < n; i++) {
        sigma[i] = 0.0; /* 默认无张力 */
        
        /* 根据曲率自动调整张力因子 */
        if (i > 0 && i < n-1) {
            real8_t d2y = (dydx[i+1] - dydx[i-1]) / (x[i+1] - x[i-1]);
            sigma[i] = fabs(d2y) * 0.1; /* 简单的张力因子估算 */
        }
    }
}
