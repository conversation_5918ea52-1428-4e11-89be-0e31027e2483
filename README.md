# PySWAP - 高性能SWAP模型Python包装器

PySWAP是SWAP (Soil-Water-Atmosphere-Plant) 4.2.0模型的高性能Python包装器，使用ISO C Binding + Cython技术实现，保持与原始Fortran代码的完全功能对等性。

## 🚀 主要特性

- **高性能**: 使用Cython优化，接近原生Fortran性能
- **完整功能**: 保持与SWAP 4.2.0的完全功能对等性
- **类型安全**: 完整的类型注解和运行时验证
- **易于使用**: 面向对象的Python接口
- **全面测试**: 包含单元测试、集成测试和性能基准测试

## 📦 安装要求

### 系统要求
- Python 3.8+
- NumPy >= 1.20.0
- Cython >= 3.0.0
- Fortran编译器 (gfortran推荐)

### 开发依赖
```bash
pip install numpy cython scipy pandas matplotlib pytest
```

## 🛠️ 编译安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd PySWAP-demo
```

### 2. 编译Cython扩展
```bash
python setup.py build_ext --inplace
```

### 3. 安装包
```bash
pip install -e .
```

## 📖 快速开始

### 基本使用
```python
import pyswap

# 创建SWAP模型实例
model = pyswap.SwapModel()

# 初始化模型
model.initialize(config_file="example.swp")

# 运行模拟
results = model.run()

# 获取结果
water_balance = results['water_balance']
soil_profile = results['soil_profile']

# 结束模型
model.finalize()
```

### 使用上下文管理器
```python
import pyswap

with pyswap.SwapModel() as model:
    model.initialize(config_dict={
        'max_nodes': 100,
        'swkmean': 6,
        'dt': 0.01
    })
    results = model.run()
    print(f"模拟完成，总水量: {results['water_balance']['total_water']:.3f} cm")
```

## 🏗️ 项目结构

```
PySWAP-demo/
├── pyswap/                 # 主Python包
│   ├── core/              # Cython核心模块
│   │   ├── functions.pyx  # 核心数学函数
│   │   ├── interpolation.pyx  # 插值计算
│   │   ├── soilwater.pyx  # 土壤水分计算
│   │   └── swap_interface.pyx  # 主模型接口
│   ├── models/            # 高级Python接口
│   │   ├── swap_model.py  # 主模型类
│   │   └── state_manager.py  # 状态管理
│   └── utils/             # 工具函数
├── c_interface/           # C接口层
├── swap420/              # 原始Fortran源码
├── tests/                # 测试套件
│   ├── unit/            # 单元测试
│   ├── integration/     # 集成测试
│   └── performance/     # 性能测试
├── examples/             # 使用示例
└── docs/                # 文档
```

## 🧪 测试

### 运行所有测试
```bash
pytest tests/
```

### 运行特定测试
```bash
# 单元测试
pytest tests/unit/

# 性能测试
pytest tests/performance/ --benchmark-only

# 集成测试
pytest tests/integration/
```

## 📊 性能基准

PySWAP的设计目标是提供接近原生Fortran的性能：

| 模块 | 相对性能 | 内存使用 |
|------|----------|----------|
| 核心数学函数 | ~95% | 相同 |
| 插值计算 | ~90% | 相同 |
| 土壤水分计算 | ~85% | 相同 |
| 完整模型 | ~80% | +10% |

## 🔧 开发指南

### 编译调试版本
```bash
python setup.py build_ext --inplace --debug
```

### 生成性能分析报告
```bash
python setup.py build_ext --inplace --annotate
# 查看生成的HTML文件了解优化机会
```

### 代码格式化
```bash
black pyswap/
flake8 pyswap/
```

## 📚 核心模块说明

### pyswap.core.functions
封装functions.f90中的核心水力特性计算函数：
- `hcomean()`: 平均水力传导度计算
- `watcon()`: 根据压力水头计算含水量
- `moiscap()`: 微分含水量计算
- `hconduc()`: 水力传导度计算

### pyswap.core.interpolation
封装sptabulated.f90中的TSPACK张力样条插值库：
- 高精度插值计算
- 表格化函数评估
- 数值微分

### pyswap.core.soilwater
封装soilwater.f90中的土壤水分状态计算：
- Richards方程求解
- 水分通量计算
- 质量平衡检查

### pyswap.models.SwapModel
高级模型接口，提供：
- 模型初始化和配置
- 时间循环管理
- 结果数据访问
- 错误处理

## ⚠️ 注意事项

1. **编译要求**: 需要Fortran编译器和适当的编译环境
2. **内存管理**: 大型模拟可能需要大量内存 (最大5000节点)
3. **数值稳定性**: 保持与原始Fortran代码相同的数值特性
4. **错误处理**: 所有Fortran错误代码都映射到Python异常

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 确保所有测试通过
5. 提交Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件

## 🙏 致谢

- SWAP模型开发团队
- Wageningen University & Research
- Cython和NumPy开发社区
