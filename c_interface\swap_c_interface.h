/*
 * SWAP模型C接口头文件
 * 
 * 定义了与Fortran SWAP模型的ISO C Binding接口
 * 保持与原始函数签名的完全兼容性
 */

#ifndef SWAP_C_INTERFACE_H
#define SWAP_C_INTERFACE_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 基本数据类型定义 */
typedef double real8_t;
typedef int32_t integer_t;
typedef bool logical_t;

/* 数组大小常量 (来自arrays.fi) */
#define MACP 5000      /* 最大土壤层数 */
#define MACROP 200     /* 最大作物数 */
#define MADAY 73200    /* 最大模拟天数 */
#define MRAIN 40000    /* 最大降雨记录数 */
#define MAHO 1000      /* 最大土层数 */
#define MATAB 1000     /* 最大表格条目数 */
#define MATABENTRIES 50005  /* 最大表格条目数 */

/* SWAP输入数据结构 (对应swap_input) */
typedef struct {
    real8_t tstart, tend;           /* 开始和结束时间 */
    real8_t tmin, tmax, hum, wind;  /* 气象数据 */
    real8_t rain, wet, etref, rad;  /* 降雨、湿度、参考蒸散发、辐射 */
    real8_t ch, zroot, lai;         /* 作物高度、根深、叶面积指数 */
    integer_t icrop;                /* 作物存在标志 */
} swap_input_t;

/* SWAP输出数据结构 (对应swap_output) */
typedef struct {
    real8_t tstart, tend;           /* 时间范围 */
    integer_t numnodes;             /* 实际节点数 */
    real8_t tpot, tact;            /* 潜在和实际蒸腾 */
    integer_t ierrorcode;           /* 错误代码 */
    real8_t dz[500];               /* 土层厚度 */
    real8_t wc[500];               /* 体积含水量 */
    real8_t rwu[500];              /* 根系吸水 */
} swap_output_t;

/* ========== 核心数学函数接口 (functions.f90) ========== */

/* 平均水力传导度计算 */
real8_t c_hcomean(integer_t swkmean, real8_t kup, real8_t klow, 
                  real8_t dzup, real8_t dzlow);

/* 根据压力水头计算含水量 */
real8_t c_watcon(integer_t node, real8_t head);

/* 计算微分含水量 */
real8_t c_moiscap(integer_t node, real8_t head);

/* 计算水力传导度 */
real8_t c_hconduc(integer_t node, real8_t head, real8_t theta, real8_t rfcp);

/* ========== 插值计算接口 (sptabulated.f90) ========== */

/* 评估表格化函数 */
void c_eval_tabulated_function(integer_t inverse, integer_t n, 
                               integer_t ind1, integer_t ind2, integer_t ind3,
                               integer_t node, real8_t sptab[][MATAB],
                               integer_t ientrytab[][MATABENTRIES+1],
                               real8_t xe, real8_t* ye, real8_t* dyedxe,
                               integer_t iwhat);

/* 预处理表格化函数 */
void c_preproc_tabulated_function(integer_t flag, integer_t n,
                                  real8_t* x, real8_t* y, 
                                  real8_t* dydx, real8_t* sigma);

/* ========== 土壤水分计算接口 (soilwater.f90) ========== */

/* 土壤水分状态计算 */
void c_soilwater(integer_t task);

/* 水量存储计算 */
void c_watstor(void);

/* 通量计算 */
void c_fluxes(void);

/* ========== 主SWAP模型接口 (swap.f90) ========== */

/* 主SWAP模型调用 */
void c_swap(integer_t icaller, integer_t itask, 
            swap_input_t* toswap, swap_output_t* fromswap);

/* ========== 状态管理接口 ========== */

/* 初始化全局状态 */
integer_t c_initialize_swap_state(void);

/* 清理全局状态 */
void c_cleanup_swap_state(void);

/* 获取状态变量 */
integer_t c_get_state_variable(const char* var_name, void* value, integer_t var_type);

/* 设置状态变量 */
integer_t c_set_state_variable(const char* var_name, const void* value, integer_t var_type);

/* ========== 错误处理接口 ========== */

/* 获取最后的错误信息 */
const char* c_get_last_error(void);

/* 清除错误状态 */
void c_clear_error(void);

/* ========== 内存管理接口 ========== */

/* 分配工作数组 */
integer_t c_allocate_work_arrays(integer_t numnod);

/* 释放工作数组 */
void c_deallocate_work_arrays(void);

/* 检查内存使用情况 */
integer_t c_check_memory_usage(void);

#ifdef __cplusplus
}
#endif

#endif /* SWAP_C_INTERFACE_H */
