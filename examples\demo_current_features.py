#!/usr/bin/env python3
"""
PySWAP当前功能演示

展示已实现的功能模块，包括：
- 状态管理系统
- 模型接口
- 错误处理
- 基本工作流程
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pyswap
from pyswap.models.state_manager import SwapState
from pyswap.models.swap_model import SwapModel
from pyswap.core.exceptions import SwapError, InitializationError


def demo_version_info():
    """演示版本信息功能"""
    print("📋 PySWAP版本信息")
    print("-" * 30)
    
    version_info = pyswap.get_version_info()
    for key, value in version_info.items():
        print(f"{key:20s}: {value}")
    
    print(f"\n主版本: {pyswap.__version__}")
    print(f"作者: {pyswap.__author__}")


def demo_environment_check():
    """演示环境检查功能"""
    print("\n🔍 环境检查")
    print("-" * 30)
    
    issues = pyswap.check_environment()
    if issues:
        print("发现的问题:")
        for issue in issues:
            print(f"  ❌ {issue}")
    else:
        print("✅ 环境检查通过")


def demo_state_manager():
    """演示状态管理器功能"""
    print("\n🏗️  状态管理器演示")
    print("-" * 30)
    
    # 1. 创建状态管理器
    print("1. 创建状态管理器...")
    state = SwapState(max_nodes=20)
    print(f"   最大节点数: {state.max_nodes}")
    
    # 2. 配置和初始化
    print("2. 初始化状态管理器...")
    config = {
        'max_nodes': 20,
        'swkmean': 6,      # 加权调和平均
        'swsophy': 0,      # 土壤物理选项
    }
    
    state.initialize(config)
    print("   ✅ 初始化完成")
    
    # 3. 检查状态摘要
    print("3. 状态摘要:")
    summary = state.get_summary()
    for key, value in summary.items():
        print(f"   {key:20s}: {value}")
    
    # 4. 验证状态
    print("4. 状态验证:")
    issues = state.validate_state()
    if issues:
        for issue in issues:
            print(f"   ⚠️  {issue}")
    else:
        print("   ✅ 所有检查通过")
    
    # 5. 访问子状态
    print("5. 子状态信息:")
    print(f"   时间状态 - 当前时间: {state.time.t}, 时间步长: {state.time.dt}")
    print(f"   土壤状态 - 节点数: {state.soilwater.numnod}, 总水量: {state.soilwater.volact}")
    print(f"   气象状态 - 温度: {state.meteo.tmn}~{state.meteo.tmx}°C")
    print(f"   作物状态 - 高度: {state.crop.ch}cm, 根深: {state.crop.zroot}cm")
    
    # 6. 重置状态
    print("6. 重置状态...")
    state.reset()
    reset_summary = state.get_summary()
    print(f"   重置后状态: {reset_summary['status']}")
    
    return state


def demo_model_interface():
    """演示模型接口功能"""
    print("\n🎯 模型接口演示")
    print("-" * 30)
    
    # 1. 创建模型
    print("1. 创建SWAP模型...")
    model = SwapModel(max_nodes=15, log_level="INFO")
    print(f"   最大节点数: {model.max_nodes}")
    
    # 2. 检查初始状态
    print("2. 初始状态:")
    initial_state = model.get_state_summary()
    for key, value in initial_state.items():
        print(f"   {key:20s}: {value}")
    
    # 3. 配置模型
    print("3. 配置模型...")
    config = {
        'max_nodes': 15,
        'swkmean': 6,
        'dt': 0.01,
        'dtmax': 1.0,
        'dtmin': 1e-6,
    }
    
    try:
        model.initialize(config_dict=config)
        print("   ✅ 模型初始化成功")
        
        # 4. 检查初始化后状态
        print("4. 初始化后状态:")
        post_init_state = model.get_state_summary()
        for key, value in post_init_state.items():
            print(f"   {key:20s}: {value}")
        
        # 5. 尝试运行模拟
        print("5. 运行模拟...")
        try:
            results = model.run()
            print("   ✅ 模拟运行成功")
            
            # 显示结果结构
            print("6. 结果结构:")
            for section, data in results.items():
                print(f"   {section}:")
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, np.ndarray):
                            print(f"     {key:15s}: 数组 {value.shape}")
                        else:
                            print(f"     {key:15s}: {value}")
                else:
                    print(f"     值: {data}")
        
        except Exception as e:
            print(f"   ⚠️  模拟运行失败: {e}")
        
        # 6. 结束模型
        print("7. 结束模型...")
        model.finalize()
        print("   ✅ 模型结束成功")
        
    except Exception as e:
        print(f"   ⚠️  模型初始化失败: {e}")
    
    return model


def demo_context_manager():
    """演示上下文管理器功能"""
    print("\n🔄 上下文管理器演示")
    print("-" * 30)
    
    try:
        with SwapModel(max_nodes=8) as model:
            print("1. 进入上下文管理器")
            
            # 在上下文中使用模型
            summary = model.get_state_summary()
            print(f"2. 模型状态: {summary['model_initialized']}")
            
            # 尝试配置
            config = {'max_nodes': 8, 'swkmean': 6}
            try:
                model.initialize(config_dict=config)
                print("3. ✅ 模型配置成功")
            except Exception as e:
                print(f"3. ⚠️  模型配置失败: {e}")
        
        print("4. ✅ 退出上下文管理器，资源自动清理")
        
    except Exception as e:
        print(f"❌ 上下文管理器演示失败: {e}")


def demo_error_handling():
    """演示错误处理功能"""
    print("\n⚠️  错误处理演示")
    print("-" * 30)
    
    # 1. 测试无效参数
    print("1. 测试无效参数...")
    try:
        state = SwapState(max_nodes=-5)  # 无效的节点数
        assert False, "应该抛出异常"
    except Exception as e:
        print(f"   ✅ 正确捕获: {type(e).__name__}: {e}")
    
    # 2. 测试超出范围
    print("2. 测试节点数超限...")
    try:
        state = SwapState(max_nodes=10000)  # 超过MACP限制
        config = {'max_nodes': 10000}
        state.initialize(config)
        assert False, "应该抛出异常"
    except Exception as e:
        print(f"   ✅ 正确捕获: {type(e).__name__}: {e}")
    
    # 3. 测试未初始化操作
    print("3. 测试未初始化操作...")
    try:
        model = SwapModel()
        model.run()  # 未初始化就运行
        assert False, "应该抛出异常"
    except Exception as e:
        print(f"   ✅ 正确捕获: {type(e).__name__}: {e}")
    
    # 4. 测试异常类型
    print("4. 异常类型测试:")
    exception_types = [
        pyswap.SwapError,
        pyswap.ConvergenceError,
        pyswap.BoundaryError,
    ]
    
    for exc_type in exception_types:
        try:
            raise exc_type("测试异常", error_code=99, module="test")
        except exc_type as e:
            print(f"   ✅ {exc_type.__name__}: {e}")


def demo_data_structures():
    """演示数据结构功能"""
    print("\n📊 数据结构演示")
    print("-" * 30)
    
    # 创建并初始化状态
    state = SwapState(max_nodes=10)
    config = {'max_nodes': 10}
    state.initialize(config)
    
    # 1. 时间控制状态
    print("1. 时间控制状态:")
    time_vars = ['t', 'tcum', 'dt', 'dtmax', 'dtmin', 'daycum', 'iyear']
    for var in time_vars:
        value = getattr(state.time, var)
        print(f"   {var:10s}: {value}")
    
    # 2. 气象状态
    print("2. 气象状态:")
    meteo_vars = ['tmn', 'tmx', 'tav', 'rad', 'grai', 'peva', 'ptra']
    for var in meteo_vars:
        value = getattr(state.meteo, var)
        print(f"   {var:10s}: {value}")
    
    # 3. 土壤水分状态
    print("3. 土壤水分状态:")
    soil_vars = ['numnod', 'qtop', 'qbot', 'pond', 'volact', 'volini']
    for var in soil_vars:
        value = getattr(state.soilwater, var)
        print(f"   {var:10s}: {value}")
    
    # 4. 数组大小
    print("4. 数组大小:")
    arrays = [
        ('h', state.soilwater.h),
        ('theta', state.soilwater.theta),
        ('k', state.soilwater.k),
        ('q', state.soilwater.q),
        ('dz', state.soilwater.dz),
    ]
    
    for name, array in arrays:
        print(f"   {name:10s}: {array.shape}")


def main():
    """主演示函数"""
    print("🚀 PySWAP当前功能演示")
    print("=" * 60)
    
    # 运行各个演示
    demos = [
        demo_version_info,
        demo_environment_check,
        demo_state_manager,
        demo_model_interface,
        demo_context_manager,
        demo_error_handling,
        demo_data_structures,
    ]
    
    for demo in demos:
        try:
            demo()
        except Exception as e:
            print(f"\n❌ 演示 {demo.__name__} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n🎉 功能演示完成!")
    print("\n📖 当前实现状态:")
    print("✅ 项目结构和配置")
    print("✅ 状态管理系统")
    print("✅ 模型接口框架")
    print("✅ 错误处理机制")
    print("✅ 测试框架")
    print("⏳ Cython扩展编译 (需要Fortran编译器)")
    print("⏳ 完整数值计算功能")
    print("⏳ 性能优化")


if __name__ == "__main__":
    main()
