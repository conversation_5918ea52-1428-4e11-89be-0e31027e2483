# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
# cython: initializedcheck=False

"""
主SWAP模型接口模块

封装swap.f90主模型接口，实现：
- swap_exchange数据结构
- 主模型调用接口 (初始化、动态、结束)
- 数据交换和状态管理

保持与原始Fortran接口的完全兼容性。
"""

import numpy as np
cimport numpy as cnp
cimport cython
from libc.string cimport memset, memcpy
from ..core.exceptions import SwapError, InitializationError

# 导入C接口声明
cdef extern from "../c_interface/swap_c_interface.h":
    ctypedef double real8_t
    ctypedef int integer_t
    ctypedef int logical_t
    
    # SWAP输入数据结构
    ctypedef struct swap_input_t:
        real8_t tstart, tend
        real8_t tmin, tmax, hum, wind, rain, wet, etref, rad
        real8_t ch, zroot, lai
        integer_t icrop
    
    # SWAP输出数据结构
    ctypedef struct swap_output_t:
        real8_t tstart, tend
        integer_t numnodes
        real8_t tpot, tact
        integer_t ierrorcode
        real8_t dz[500]
        real8_t wc[500]
        real8_t rwu[500]
    
    # 主SWAP接口函数
    void c_swap(integer_t icaller, integer_t itask, 
                swap_input_t* toswap, swap_output_t* fromswap)
    
    # 状态管理函数
    integer_t c_initialize_swap_state()
    void c_cleanup_swap_state()


cdef class SwapInput:
    """
    SWAP输入数据类
    
    对应Fortran的swap_input类型，包含：
    - 时间范围 (tstart, tend)
    - 气象数据 (温度、湿度、风速、降雨、辐射等)
    - 作物参数 (高度、根深、叶面积指数)
    """
    
    cdef swap_input_t _data
    
    def __init__(self):
        """初始化输入数据结构"""
        memset(&self._data, 0, sizeof(swap_input_t))
        
        # 设置默认值
        self._data.tstart = 0.0
        self._data.tend = 1.0
        self._data.tmin = 10.0
        self._data.tmax = 20.0
        self._data.hum = 1.0
        self._data.wind = 2.0
        self._data.rain = 0.0
        self._data.wet = 0.0
        self._data.etref = 3.0
        self._data.rad = 15000.0
        self._data.ch = 50.0
        self._data.zroot = 30.0
        self._data.lai = 2.0
        self._data.icrop = 1
    
    @property
    def tstart(self):
        """开始时间 (自1900年的天数)"""
        return self._data.tstart
    
    @tstart.setter
    def tstart(self, value):
        self._data.tstart = value
    
    @property
    def tend(self):
        """结束时间 (自1900年的天数)"""
        return self._data.tend
    
    @tend.setter
    def tend(self, value):
        self._data.tend = value
    
    @property
    def tmin(self):
        """最低温度 (°C)"""
        return self._data.tmin
    
    @tmin.setter
    def tmin(self, value):
        self._data.tmin = value
    
    @property
    def tmax(self):
        """最高温度 (°C)"""
        return self._data.tmax
    
    @tmax.setter
    def tmax(self, value):
        self._data.tmax = value
    
    @property
    def hum(self):
        """湿度 (kPa)"""
        return self._data.hum
    
    @hum.setter
    def hum(self, value):
        self._data.hum = value
    
    @property
    def wind(self):
        """风速 (m/s)"""
        return self._data.wind
    
    @wind.setter
    def wind(self, value):
        self._data.wind = value
    
    @property
    def rain(self):
        """降雨量 (mm/d)"""
        return self._data.rain
    
    @rain.setter
    def rain(self, value):
        self._data.rain = value
    
    @property
    def wet(self):
        """湿润时间 (-)"""
        return self._data.wet
    
    @wet.setter
    def wet(self, value):
        self._data.wet = value
    
    @property
    def etref(self):
        """参考蒸散发 (mm/d)"""
        return self._data.etref
    
    @etref.setter
    def etref(self, value):
        self._data.etref = value
    
    @property
    def rad(self):
        """辐射 (kJ/m²/d)"""
        return self._data.rad
    
    @rad.setter
    def rad(self, value):
        self._data.rad = value
    
    @property
    def ch(self):
        """作物高度 (cm)"""
        return self._data.ch
    
    @ch.setter
    def ch(self, value):
        self._data.ch = value
    
    @property
    def zroot(self):
        """根深 (cm)"""
        return self._data.zroot
    
    @zroot.setter
    def zroot(self, value):
        self._data.zroot = value
    
    @property
    def lai(self):
        """叶面积指数 (m²/m²)"""
        return self._data.lai
    
    @lai.setter
    def lai(self, value):
        self._data.lai = value
    
    @property
    def icrop(self):
        """作物存在标志 (0/1)"""
        return self._data.icrop
    
    @icrop.setter
    def icrop(self, value):
        self._data.icrop = value
    
    def to_dict(self):
        """转换为字典"""
        return {
            'tstart': self.tstart,
            'tend': self.tend,
            'tmin': self.tmin,
            'tmax': self.tmax,
            'hum': self.hum,
            'wind': self.wind,
            'rain': self.rain,
            'wet': self.wet,
            'etref': self.etref,
            'rad': self.rad,
            'ch': self.ch,
            'zroot': self.zroot,
            'lai': self.lai,
            'icrop': self.icrop,
        }
    
    def from_dict(self, data):
        """从字典设置数据"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)


cdef class SwapOutput:
    """
    SWAP输出数据类
    
    对应Fortran的swap_output类型，包含：
    - 时间范围
    - 节点数和错误代码
    - 蒸腾数据
    - 土壤剖面数据 (层厚、含水量、根系吸水)
    """
    
    cdef swap_output_t _data
    
    def __init__(self):
        """初始化输出数据结构"""
        memset(&self._data, 0, sizeof(swap_output_t))
    
    @property
    def tstart(self):
        """开始时间"""
        return self._data.tstart
    
    @property
    def tend(self):
        """结束时间"""
        return self._data.tend
    
    @property
    def numnodes(self):
        """实际节点数"""
        return self._data.numnodes
    
    @property
    def tpot(self):
        """潜在蒸腾 (mm/d)"""
        return self._data.tpot
    
    @property
    def tact(self):
        """实际蒸腾 (mm/d)"""
        return self._data.tact
    
    @property
    def ierrorcode(self):
        """错误代码"""
        return self._data.ierrorcode
    
    def get_layer_thickness(self):
        """获取层厚数组"""
        cdef cnp.ndarray[real8_t, ndim=1] result = np.empty(self._data.numnodes, dtype=np.float64)
        for i in range(self._data.numnodes):
            result[i] = self._data.dz[i]
        return result
    
    def get_water_content(self):
        """获取含水量数组"""
        cdef cnp.ndarray[real8_t, ndim=1] result = np.empty(self._data.numnodes, dtype=np.float64)
        for i in range(self._data.numnodes):
            result[i] = self._data.wc[i]
        return result
    
    def get_root_water_uptake(self):
        """获取根系吸水数组"""
        cdef cnp.ndarray[real8_t, ndim=1] result = np.empty(self._data.numnodes, dtype=np.float64)
        for i in range(self._data.numnodes):
            result[i] = self._data.rwu[i]
        return result
    
    def to_dict(self):
        """转换为字典"""
        return {
            'tstart': self.tstart,
            'tend': self.tend,
            'numnodes': self.numnodes,
            'tpot': self.tpot,
            'tact': self.tact,
            'ierrorcode': self.ierrorcode,
            'dz': self.get_layer_thickness(),
            'wc': self.get_water_content(),
            'rwu': self.get_root_water_uptake(),
        }


@cython.boundscheck(False)
@cython.wraparound(False)
def call_swap_model(int icaller, int itask, SwapInput input_data=None):
    """
    调用主SWAP模型
    
    对应Fortran子程序: swap(icaller, itask, toswap, fromswap)
    
    Parameters:
    -----------
    icaller : int
        调用者标识 (0=从swap_main调用, 其他=DLL调用)
    itask : int
        任务类型 (1=初始化, 2=动态, 3=结束)
    input_data : SwapInput, optional
        输入数据 (任务1和2需要)
        
    Returns:
    --------
    SwapOutput
        输出数据结构
        
    Raises:
    -------
    ValueError
        当输入参数无效时
    SwapError
        当SWAP计算失败时
    """
    if itask not in [1, 2, 3]:
        raise ValueError(f"无效的任务类型: {itask}，必须是1、2或3")
    
    if icaller != 0 and itask < 3 and input_data is None:
        raise ValueError("DLL调用需要提供输入数据")
    
    # 准备输入输出结构
    cdef swap_input_t* toswap = NULL
    cdef swap_output_t fromswap
    
    if input_data is not None:
        toswap = &input_data._data
    
    memset(&fromswap, 0, sizeof(swap_output_t))
    
    # 调用C接口
    try:
        c_swap(<integer_t>icaller, <integer_t>itask, toswap, &fromswap)
    except Exception as e:
        raise SwapError(f"SWAP模型调用失败: {e}", module="swap_interface")
    
    # 检查错误代码
    if fromswap.ierrorcode != 0:
        raise SwapError(f"SWAP计算失败", 
                       error_code=fromswap.ierrorcode,
                       module="swap_model")
    
    # 创建输出对象
    output = SwapOutput()
    memcpy(&output._data, &fromswap, sizeof(swap_output_t))
    
    return output


def initialize_swap_state():
    """
    初始化SWAP全局状态
    
    Returns:
    --------
    int
        错误代码 (0=成功)
    """
    cdef integer_t result = c_initialize_swap_state()
    return result


def cleanup_swap_state():
    """清理SWAP全局状态"""
    c_cleanup_swap_state()
