["tests/performance/test_benchmark.py::TestCoreFunctionPerformance::test_batch_vs_single_calls", "tests/performance/test_benchmark.py::TestCoreFunctionPerformance::test_hcomean_performance", "tests/performance/test_benchmark.py::TestModelPerformance::test_memory_usage", "tests/performance/test_benchmark.py::TestModelPerformance::test_model_creation_time", "tests/performance/test_benchmark.py::TestStateManagerPerformance::test_state_initialization_time", "tests/performance/test_benchmark.py::TestStateManagerPerformance::test_state_validation_time", "tests/unit/test_functions.py::TestBatchFunctions::test_batch_hconduc", "tests/unit/test_functions.py::TestBatchFunctions::test_batch_watcon", "tests/unit/test_functions.py::TestBatchFunctions::test_mismatched_arrays", "tests/unit/test_functions.py::TestHcomean::test_arithmetic_mean_unweighted", "tests/unit/test_functions.py::TestHcomean::test_arithmetic_mean_weighted", "tests/unit/test_functions.py::TestHcomean::test_geometric_mean_unweighted", "tests/unit/test_functions.py::TestHcomean::test_geometric_mean_weighted", "tests/unit/test_functions.py::TestHcomean::test_harmonic_mean_unweighted", "tests/unit/test_functions.py::TestHcomean::test_harmonic_mean_weighted", "tests/unit/test_functions.py::TestHcomean::test_invalid_method", "tests/unit/test_functions.py::TestHcomean::test_negative_conductivity", "tests/unit/test_functions.py::TestHcomean::test_zero_thickness", "tests/unit/test_functions.py::TestNumericalAccuracy::test_extreme_values", "tests/unit/test_functions.py::TestNumericalAccuracy::test_hcomean_precision", "tests/unit/test_functions.py::TestWatcon::test_basic_calculation", "tests/unit/test_functions.py::TestWatcon::test_invalid_node", "tests/unit/test_functions.py::TestWatcon::test_saturated_conditions"]