[build-system]
requires = [
    "setuptools>=61.0",
    "wheel",
    "Cython>=3.0.0",
    "numpy>=1.20.0",
]
build-backend = "setuptools.build_meta"

[project]
name = "pyswap"
version = "0.1.0"
description = "High-performance Python wrapper for SWAP soil-water-atmosphere-plant model"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "PySWAP Development Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Fortran",
    "Programming Language :: Cython",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Hydrology",
]
keywords = ["hydrology", "soil", "water", "agriculture", "modeling", "simulation"]
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.20.0",
    "scipy>=1.7.0",
    "pandas>=1.3.0",
    "matplotlib>=3.4.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "black",
    "flake8",
    "mypy",
    "sphinx",
    "sphinx-rtd-theme",
    "jupyter",
    "ipython",
    "cython",
]
test = [
    "pytest>=6.0",
    "pytest-cov",
    "pytest-benchmark",
]
docs = [
    "sphinx",
    "sphinx-rtd-theme",
    "numpydoc",
    "myst-parser",
]

[project.urls]
Homepage = "https://github.com/pyswap/pyswap"
Documentation = "https://pyswap.readthedocs.io"
Repository = "https://github.com/pyswap/pyswap"
"Bug Tracker" = "https://github.com/pyswap/pyswap/issues"

[tool.setuptools]
packages = ["pyswap"]

[tool.setuptools.package-data]
pyswap = ["*.pyx", "*.pxd", "*.c", "*.h"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
