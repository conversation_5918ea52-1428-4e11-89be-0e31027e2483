# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
# cython: initializedcheck=False

"""
核心数学函数模块

封装functions.f90中的核心水力特性计算函数，包括：
- hcomean: 平均水力传导度计算
- watcon: 根据压力水头计算含水量  
- moiscap: 微分含水量计算
- hconduc: 水力传导度计算

保持与原始Fortran函数的完全兼容性和数值精度。
"""

import numpy as np
cimport numpy as cnp
cimport cython
from libc.math cimport sqrt, exp, log, pow
from ..core.exceptions import SwapError, ConvergenceError

# 导入C接口声明
cdef extern from "../c_interface/swap_c_interface.h":
    ctypedef double real8_t
    ctypedef int integer_t
    
    # 核心数学函数的C接口声明
    real8_t c_hcomean(integer_t swkmean, real8_t kup, real8_t klow, 
                      real8_t dzup, real8_t dzlow)
    real8_t c_watcon(integer_t node, real8_t head)
    real8_t c_moiscap(integer_t node, real8_t head)
    real8_t c_hconduc(integer_t node, real8_t head, real8_t theta, real8_t rfcp)


@cython.boundscheck(False)
@cython.wraparound(False)
def hcomean(int swkmean, double kup, double klow, double dzup, double dzlow):
    """
    计算平均水力传导度
    
    Parameters:
    -----------
    swkmean : int
        平均方法选择 (1-6)
        1: 非加权算术平均
        2: 加权算术平均
        3: 非加权几何平均
        4: 加权几何平均
        5: 非加权调和平均
        6: 加权调和平均
    kup : float
        上层水力传导度 (cm/d)
    klow : float
        下层水力传导度 (cm/d)
    dzup : float
        上层厚度 (cm)
    dzlow : float
        下层厚度 (cm)
        
    Returns:
    --------
    float
        平均水力传导度 (cm/d)
        
    Raises:
    -------
    ValueError
        当输入参数无效时
    """
    if swkmean < 1 or swkmean > 6:
        raise ValueError(f"无效的平均方法: {swkmean}，必须在1-6之间")
    
    if kup < 0 or klow < 0:
        raise ValueError("水力传导度不能为负值")
        
    if dzup <= 0 or dzlow <= 0:
        raise ValueError("土层厚度必须为正值")
    
    cdef real8_t result = c_hcomean(<integer_t>swkmean, <real8_t>kup, <real8_t>klow,
                                    <real8_t>dzup, <real8_t>dzlow)
    return result


@cython.boundscheck(False)
@cython.wraparound(False)
def watcon(int node, double head):
    """
    根据压力水头计算体积含水量
    
    Parameters:
    -----------
    node : int
        节点编号 (1-based)
    head : float
        压力水头 (cm)
        
    Returns:
    --------
    float
        体积含水量 (cm³/cm³)
        
    Raises:
    -------
    ValueError
        当节点编号无效时
    SwapError
        当计算失败时
    """
    if node < 1:
        raise ValueError(f"无效的节点编号: {node}，必须 >= 1")
    
    cdef real8_t result = c_watcon(<integer_t>node, <real8_t>head)
    
    # 检查结果的合理性
    if result < 0 or result > 1:
        raise SwapError(f"计算的含水量超出合理范围: {result}", module="watcon")
    
    return result


@cython.boundscheck(False)
@cython.wraparound(False)
def moiscap(int node, double head):
    """
    计算微分含水量 (dθ/dh)
    
    Parameters:
    -----------
    node : int
        节点编号 (1-based)
    head : float
        压力水头 (cm)
        
    Returns:
    --------
    float
        微分含水量 (cm⁻¹)
        
    Raises:
    -------
    ValueError
        当节点编号无效时
    SwapError
        当计算失败时
    """
    if node < 1:
        raise ValueError(f"无效的节点编号: {node}，必须 >= 1")
    
    cdef real8_t result = c_moiscap(<integer_t>node, <real8_t>head)
    
    # 检查结果的合理性
    if result < 0:
        raise SwapError(f"计算的微分含水量为负值: {result}", module="moiscap")
    
    return result


@cython.boundscheck(False)
@cython.wraparound(False)
def hconduc(int node, double head, double theta, double rfcp):
    """
    计算水力传导度
    
    Parameters:
    -----------
    node : int
        节点编号 (1-based)
    head : float
        压力水头 (cm)
    theta : float
        体积含水量 (cm³/cm³)
    rfcp : float
        冻结条件下的传导度折减因子 (-)
        
    Returns:
    --------
    float
        水力传导度 (cm/d)
        
    Raises:
    -------
    ValueError
        当输入参数无效时
    SwapError
        当计算失败时
    """
    if node < 1:
        raise ValueError(f"无效的节点编号: {node}，必须 >= 1")
    
    if theta < 0 or theta > 1:
        raise ValueError(f"无效的含水量: {theta}，必须在0-1之间")
        
    if rfcp < 0 or rfcp > 1:
        raise ValueError(f"无效的折减因子: {rfcp}，必须在0-1之间")
    
    cdef real8_t result = c_hconduc(<integer_t>node, <real8_t>head, 
                                    <real8_t>theta, <real8_t>rfcp)
    
    # 检查结果的合理性
    if result < 0:
        raise SwapError(f"计算的水力传导度为负值: {result}", module="hconduc")
    
    return result


# 批量计算函数，提高性能
@cython.boundscheck(False)
@cython.wraparound(False)
def batch_watcon(cnp.ndarray[integer_t, ndim=1] nodes, 
                 cnp.ndarray[real8_t, ndim=1] heads):
    """
    批量计算多个节点的含水量
    
    Parameters:
    -----------
    nodes : ndarray[int]
        节点编号数组
    heads : ndarray[float]
        压力水头数组
        
    Returns:
    --------
    ndarray[float]
        含水量数组
    """
    cdef int n = nodes.shape[0]
    if heads.shape[0] != n:
        raise ValueError("节点数组和水头数组长度不匹配")
    
    cdef cnp.ndarray[real8_t, ndim=1] result = np.empty(n, dtype=np.float64)
    cdef int i
    
    for i in range(n):
        result[i] = c_watcon(nodes[i], heads[i])
        
    return result


@cython.boundscheck(False)
@cython.wraparound(False)
def batch_hconduc(cnp.ndarray[integer_t, ndim=1] nodes,
                  cnp.ndarray[real8_t, ndim=1] heads,
                  cnp.ndarray[real8_t, ndim=1] thetas,
                  cnp.ndarray[real8_t, ndim=1] rfcps):
    """
    批量计算多个节点的水力传导度
    
    Parameters:
    -----------
    nodes : ndarray[int]
        节点编号数组
    heads : ndarray[float]
        压力水头数组
    thetas : ndarray[float]
        含水量数组
    rfcps : ndarray[float]
        折减因子数组
        
    Returns:
    --------
    ndarray[float]
        水力传导度数组
    """
    cdef int n = nodes.shape[0]
    if (heads.shape[0] != n or thetas.shape[0] != n or rfcps.shape[0] != n):
        raise ValueError("所有输入数组长度必须相同")
    
    cdef cnp.ndarray[real8_t, ndim=1] result = np.empty(n, dtype=np.float64)
    cdef int i
    
    for i in range(n):
        result[i] = c_hconduc(nodes[i], heads[i], thetas[i], rfcps[i])
        
    return result
