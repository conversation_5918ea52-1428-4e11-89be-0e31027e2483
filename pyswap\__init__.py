"""
PySWAP - 高性能SWAP土壤-水分-大气-植物模型Python包装器

本包为SWAP (Soil-Water-Atmosphere-Plant) 4.2.0模型提供高性能Python绑定，
使用ISO C Binding + Cython技术实现，保持与原始Fortran代码的完全功能对等性。

主要组件:
- core: 核心计算模块 (functions, interpolation, soilwater, swap_interface)
- models: 高级模型接口和状态管理
- utils: 工具函数和辅助功能

使用示例:
    >>> import pyswap
    >>> model = pyswap.SwapModel()
    >>> model.initialize(config_file="example.swp")
    >>> results = model.run()
"""

__version__ = "0.1.0"
__author__ = "PySWAP Development Team"
__email__ = "<EMAIL>"

# 导入主要类和函数
try:
    from .models.swap_model import SwapModel
    from .models.state_manager import SwapState
    from .core.exceptions import SwapError, ConvergenceError, BoundaryError
except ImportError:
    # 如果模块尚未编译，提供占位符
    SwapModel = None
    SwapState = None
    SwapError = Exception
    ConvergenceError = Exception
    BoundaryError = Exception

# 核心计算模块（编译后可用）
try:
    from .core import functions
    from .core import interpolation
    from .core import soilwater
    from .core import swap_interface
    _COMPILED_MODULES_AVAILABLE = True
except ImportError:
    _COMPILED_MODULES_AVAILABLE = False
    import warnings
    warnings.warn(
        "编译的Cython模块不可用。请运行 'python setup.py build_ext --inplace' 来编译扩展模块。",
        ImportWarning
    )

# 工具函数
try:
    from .utils.config import load_config, validate_config
    from .utils.io import read_meteo_data, write_output
except ImportError:
    # 提供占位符函数
    def load_config(*args, **kwargs):
        raise ImportError("工具模块尚未实现")
    def validate_config(*args, **kwargs):
        raise ImportError("工具模块尚未实现")
    def read_meteo_data(*args, **kwargs):
        raise ImportError("工具模块尚未实现")
    def write_output(*args, **kwargs):
        raise ImportError("工具模块尚未实现")

__all__ = [
    # 主要类
    'SwapModel',
    'SwapState',
    
    # 异常类
    'SwapError',
    'ConvergenceError', 
    'BoundaryError',
    
    # 工具函数
    'load_config',
    'validate_config',
    'read_meteo_data',
    'write_output',
    
    # 模块可用性标志
    '_COMPILED_MODULES_AVAILABLE',
]

def get_version_info():
    """返回详细的版本信息"""
    info = {
        'version': __version__,
        'compiled_modules': _COMPILED_MODULES_AVAILABLE,
        'numpy_version': None,
        'cython_available': False,
    }
    
    try:
        import numpy as np
        info['numpy_version'] = np.__version__
    except ImportError:
        pass
        
    try:
        import Cython
        info['cython_available'] = True
        info['cython_version'] = Cython.__version__
    except ImportError:
        pass
    
    return info

def check_environment():
    """检查开发环境是否正确配置"""
    issues = []
    
    try:
        import numpy
    except ImportError:
        issues.append("NumPy未安装")
    
    try:
        import Cython
    except ImportError:
        issues.append("Cython未安装")
    
    if not _COMPILED_MODULES_AVAILABLE:
        issues.append("Cython扩展模块未编译")
    
    return issues
