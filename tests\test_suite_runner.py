#!/usr/bin/env python3
"""
PySWAP测试套件运行器

运行完整的测试套件，包括：
- 单元测试
- 集成测试  
- 性能测试
- 数值验证测试
"""

import sys
import subprocess
import time
from pathlib import Path
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import pyswap
    PYSWAP_AVAILABLE = True
except ImportError:
    PYSWAP_AVAILABLE = False


def run_unit_tests():
    """运行单元测试"""
    print("🧪 运行单元测试...")
    print("-" * 40)
    
    if not PYSWAP_AVAILABLE:
        print("❌ PySWAP不可用，跳过单元测试")
        return False
    
    try:
        # 运行pytest单元测试
        cmd = [sys.executable, "-m", "pytest", "tests/unit/", "-v", "--tb=short"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print("单元测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"单元测试结果: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 单元测试超时")
        return False
    except Exception as e:
        print(f"❌ 单元测试执行失败: {e}")
        return False


def run_integration_tests():
    """运行集成测试"""
    print("\n🔗 运行集成测试...")
    print("-" * 40)
    
    try:
        # 直接运行集成测试脚本
        cmd = [sys.executable, "tests/integration/test_full_workflow.py"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print("集成测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"集成测试结果: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 集成测试超时")
        return False
    except Exception as e:
        print(f"❌ 集成测试执行失败: {e}")
        return False


def run_performance_tests():
    """运行性能测试"""
    print("\n⚡ 运行性能测试...")
    print("-" * 40)
    
    if not PYSWAP_AVAILABLE:
        print("❌ PySWAP不可用，跳过性能测试")
        return False
    
    try:
        # 运行性能基准测试
        cmd = [sys.executable, "tests/performance/test_benchmark.py"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        print("性能测试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"性能测试结果: {'✅ 通过' if success else '❌ 失败'}")
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 性能测试超时")
        return False
    except Exception as e:
        print(f"❌ 性能测试执行失败: {e}")
        return False


def run_numerical_validation():
    """运行数值验证测试"""
    print("\n🔢 运行数值验证测试...")
    print("-" * 40)

    if not PYSWAP_AVAILABLE:
        print("❌ PySWAP不可用，跳过数值验证")
        return False

    try:
        # 导入必要的模块
        import numpy as np
        from pyswap.models.state_manager import SwapState

        # 创建测试状态
        state = SwapState(max_nodes=10)
        config = {'max_nodes': 10, 'swkmean': 6}
        state.initialize(config)

        # 验证数组初始化
        assert len(state.soilwater.h) == 10
        assert len(state.soilwater.theta) == 10
        assert len(state.soilwater.k) == 10
        assert len(state.soilwater.q) == 11

        print("✅ 数组大小验证通过")

        # 验证物理约束
        assert np.all(state.soilwater.theta >= 0.0)
        assert np.all(state.soilwater.theta <= 1.0)
        assert np.all(state.soilwater.k >= 0.0)

        print("✅ 物理约束验证通过")

        # 验证状态一致性
        issues = state.validate_state()
        if issues:
            print(f"⚠️  发现 {len(issues)} 个状态问题")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ 状态一致性验证通过")

        return len(issues) == 0

    except Exception as e:
        print(f"❌ 数值验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_environment_check():
    """运行环境检查"""
    print("\n🔍 环境检查...")
    print("-" * 40)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
    
    # 检查必要的包
    required_packages = {
        'numpy': '1.20.0',
        'scipy': '1.7.0', 
        'matplotlib': '3.4.0',
        'cython': '3.0.0',
    }
    
    missing_packages = []
    
    for package, min_version in required_packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        return False
    
    # 检查PySWAP状态
    if PYSWAP_AVAILABLE:
        print("✅ PySWAP可用")
        
        try:
            version_info = pyswap.get_version_info()
            print(f"   版本: {version_info['version']}")
            print(f"   编译模块: {version_info['compiled_modules']}")
            
            issues = pyswap.check_environment()
            if issues:
                print("   环境问题:")
                for issue in issues:
                    print(f"     - {issue}")
            else:
                print("   ✅ 环境检查通过")
                
        except Exception as e:
            print(f"   ⚠️  环境检查失败: {e}")
    else:
        print("❌ PySWAP不可用")
        return False
    
    return True


def generate_test_report(results):
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    print("-" * 40)
    
    report = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'environment': {
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'pyswap_available': PYSWAP_AVAILABLE,
        },
        'test_results': results,
        'summary': {
            'total_tests': len(results),
            'passed_tests': sum(1 for r in results.values() if r),
            'failed_tests': sum(1 for r in results.values() if not r),
        }
    }
    
    if PYSWAP_AVAILABLE:
        try:
            report['environment']['pyswap_version'] = pyswap.__version__
            report['environment']['compiled_modules'] = pyswap._COMPILED_MODULES_AVAILABLE
        except:
            pass
    
    # 保存报告
    report_file = "test_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 测试报告已保存到 {report_file}")
    
    # 显示摘要
    summary = report['summary']
    print(f"\n📋 测试摘要:")
    print(f"   总测试数: {summary['total_tests']}")
    print(f"   通过: {summary['passed_tests']}")
    print(f"   失败: {summary['failed_tests']}")
    print(f"   成功率: {summary['passed_tests']/summary['total_tests']*100:.1f}%")
    
    return report


def main():
    """主函数"""
    print("🚀 PySWAP测试套件运行器")
    print("=" * 60)
    
    start_time = time.time()
    
    # 运行各项测试
    test_results = {}
    
    # 1. 环境检查
    test_results['environment_check'] = run_environment_check()
    
    # 2. 数值验证
    test_results['numerical_validation'] = run_numerical_validation()
    
    # 3. 单元测试
    test_results['unit_tests'] = run_unit_tests()
    
    # 4. 集成测试
    test_results['integration_tests'] = run_integration_tests()
    
    # 5. 性能测试
    test_results['performance_tests'] = run_performance_tests()
    
    # 生成报告
    report = generate_test_report(test_results)
    
    # 总结
    elapsed_time = time.time() - start_time
    print(f"\n⏱️  总测试时间: {elapsed_time:.1f} 秒")
    
    passed_count = sum(1 for r in test_results.values() if r)
    total_count = len(test_results)
    
    if passed_count == total_count:
        print("🎉 所有测试通过!")
        return True
    else:
        print(f"⚠️  {total_count - passed_count}/{total_count} 测试失败")
        print("\n📖 建议:")
        
        if not test_results.get('environment_check', False):
            print("   - 检查并安装缺失的依赖包")
        
        if not PYSWAP_AVAILABLE:
            print("   - 确保PySWAP包正确安装")
        
        if not pyswap._COMPILED_MODULES_AVAILABLE if PYSWAP_AVAILABLE else True:
            print("   - 编译Cython扩展模块")
            print("   - 安装Fortran编译器 (gfortran)")
        
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
