{"Version": 1, "WorkspaceRootPath": "D:\\code\\Python_item\\PySWAP-demo\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\code\\Python_item\\PySWAP-demo\\swap420\\soilwater.f90||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:swap420\\soilwater.f90||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "soilwater.f90", "DocumentMoniker": "D:\\code\\Python_item\\PySWAP-demo\\swap420\\soilwater.f90", "RelativeDocumentMoniker": "swap420\\soilwater.f90", "ToolTip": "D:\\code\\Python_item\\PySWAP-demo\\swap420\\soilwater.f90", "RelativeToolTip": "swap420\\soilwater.f90", "ViewState": "AgIAABIAAAAAAAAAAAAAAB8AAAALAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAC5SURBVEhL7ZO9DcMgEIUf6XxzUVkMEXkCRmGCKEOgVMwFJSkikHLhx3Zw0viTKHyc9enZeiLGCAAQQmAr3vvXyxVomiCSIITQXC5wB3DlQ843gsyDbnyE2S9ASaCUet8sIKWE1jo/twQXfrEG5xyMMXyM2S/5JKoJrLV5icN3eIIkICKxK8EWhgr458FoQYld/6AHEeXWHp5giEApVe3PEEGLU9DlFHSpNnkPqf0/bfJHghH8J8FRPAFpM2GbpGwBHAAAAABJRU5ErkJggg==", "WhenOpened": "2025-08-18T15:18:48.51Z", "EditorCaption": ""}]}]}]}