"""
核心数学函数单元测试

测试functions.f90中封装的核心水力特性计算函数，
验证数值精度和边界条件处理。
"""

import pytest
import numpy as np
from numpy.testing import assert_allclose, assert_array_less

# 尝试导入编译的模块，如果不可用则跳过测试
try:
    from pyswap.core import functions
    FUNCTIONS_AVAILABLE = True
except ImportError:
    FUNCTIONS_AVAILABLE = False

from pyswap.core.exceptions import SwapError


@pytest.mark.skipif(not FUNCTIONS_AVAILABLE, reason="functions模块未编译")
class TestHcomean:
    """测试平均水力传导度计算函数"""
    
    def test_arithmetic_mean_unweighted(self):
        """测试非加权算术平均"""
        result = functions.hcomean(1, 10.0, 20.0, 5.0, 5.0)
        expected = 15.0  # (10 + 20) / 2
        assert_allclose(result, expected, rtol=1e-10)
    
    def test_arithmetic_mean_weighted(self):
        """测试加权算术平均"""
        result = functions.hcomean(2, 10.0, 20.0, 2.0, 8.0)
        expected = (2.0 * 10.0 + 8.0 * 20.0) / (2.0 + 8.0)  # 18.0
        assert_allclose(result, expected, rtol=1e-10)
    
    def test_geometric_mean_unweighted(self):
        """测试非加权几何平均"""
        result = functions.hcomean(3, 4.0, 9.0, 5.0, 5.0)
        expected = np.sqrt(4.0 * 9.0)  # 6.0
        assert_allclose(result, expected, rtol=1e-10)
    
    def test_geometric_mean_weighted(self):
        """测试加权几何平均"""
        result = functions.hcomean(4, 8.0, 2.0, 3.0, 1.0)
        a1 = 3.0 / (3.0 + 1.0)  # 0.75
        a2 = 1.0 - a1           # 0.25
        expected = (8.0 ** a1) * (2.0 ** a2)
        assert_allclose(result, expected, rtol=1e-10)
    
    def test_harmonic_mean_unweighted(self):
        """测试非加权调和平均"""
        result = functions.hcomean(5, 6.0, 12.0, 5.0, 5.0)
        expected = 1.0 / (0.5/6.0 + 0.5/12.0)  # 8.0
        assert_allclose(result, expected, rtol=1e-10)
    
    def test_harmonic_mean_weighted(self):
        """测试加权调和平均"""
        result = functions.hcomean(6, 6.0, 12.0, 2.0, 4.0)
        a1 = 2.0 / (2.0 + 4.0)  # 1/3
        a2 = 1.0 - a1           # 2/3
        expected = 1.0 / (a1/6.0 + a2/12.0)
        assert_allclose(result, expected, rtol=1e-10)
    
    def test_invalid_method(self):
        """测试无效的平均方法"""
        with pytest.raises(ValueError, match="无效的平均方法"):
            functions.hcomean(0, 10.0, 20.0, 5.0, 5.0)
        
        with pytest.raises(ValueError, match="无效的平均方法"):
            functions.hcomean(7, 10.0, 20.0, 5.0, 5.0)
    
    def test_negative_conductivity(self):
        """测试负传导度输入"""
        with pytest.raises(ValueError, match="水力传导度不能为负值"):
            functions.hcomean(1, -1.0, 20.0, 5.0, 5.0)
    
    def test_zero_thickness(self):
        """测试零厚度输入"""
        with pytest.raises(ValueError, match="土层厚度必须为正值"):
            functions.hcomean(1, 10.0, 20.0, 0.0, 5.0)


@pytest.mark.skipif(not FUNCTIONS_AVAILABLE, reason="functions模块未编译")
class TestWatcon:
    """测试含水量计算函数"""
    
    def test_basic_calculation(self):
        """测试基本含水量计算"""
        # 注意：这需要先初始化土壤参数
        # 这里只测试接口调用
        try:
            result = functions.watcon(1, -100.0)
            assert 0.0 <= result <= 1.0, "含水量应在0-1之间"
        except SwapError:
            # 如果全局状态未初始化，这是预期的
            pass
    
    def test_invalid_node(self):
        """测试无效节点编号"""
        with pytest.raises(ValueError, match="无效的节点编号"):
            functions.watcon(0, -100.0)
    
    def test_saturated_conditions(self):
        """测试饱和条件"""
        try:
            result = functions.watcon(1, 0.0)
            # 在饱和条件下，含水量应接近饱和含水量
            assert result > 0.3, "饱和条件下含水量应较高"
        except SwapError:
            pass


@pytest.mark.skipif(not FUNCTIONS_AVAILABLE, reason="functions模块未编译")
class TestBatchFunctions:
    """测试批量计算函数"""
    
    def test_batch_watcon(self):
        """测试批量含水量计算"""
        nodes = np.array([1, 2, 3, 4, 5], dtype=np.int32)
        heads = np.array([-10.0, -50.0, -100.0, -500.0, -1000.0])
        
        try:
            results = functions.batch_watcon(nodes, heads)
            assert len(results) == len(nodes)
            assert_array_less(0.0, results)  # 所有结果应为正
            assert_array_less(results, 1.0)  # 所有结果应小于1
        except SwapError:
            # 如果全局状态未初始化，这是预期的
            pass
    
    def test_batch_hconduc(self):
        """测试批量水力传导度计算"""
        nodes = np.array([1, 2, 3], dtype=np.int32)
        heads = np.array([-10.0, -50.0, -100.0])
        thetas = np.array([0.4, 0.3, 0.2])
        rfcps = np.array([1.0, 1.0, 1.0])
        
        try:
            results = functions.batch_hconduc(nodes, heads, thetas, rfcps)
            assert len(results) == len(nodes)
            assert_array_less(-0.001, results)  # 结果应非负（允许小的数值误差）
        except SwapError:
            # 如果全局状态未初始化，这是预期的
            pass
    
    def test_mismatched_arrays(self):
        """测试数组长度不匹配"""
        nodes = np.array([1, 2], dtype=np.int32)
        heads = np.array([-10.0, -50.0, -100.0])  # 长度不匹配
        
        with pytest.raises(ValueError, match="长度不匹配"):
            functions.batch_watcon(nodes, heads)


@pytest.mark.skipif(not FUNCTIONS_AVAILABLE, reason="functions模块未编译")
class TestNumericalAccuracy:
    """测试数值精度"""
    
    def test_hcomean_precision(self):
        """测试hcomean的数值精度"""
        # 使用已知的精确值进行测试
        k1, k2 = 1e-6, 1e6  # 极端值
        dz1, dz2 = 1.0, 1.0
        
        # 几何平均应该是1.0
        result = functions.hcomean(3, k1, k2, dz1, dz2)
        assert_allclose(result, 1.0, rtol=1e-10)
    
    def test_extreme_values(self):
        """测试极端值处理"""
        # 非常小的传导度
        result = functions.hcomean(1, 1e-12, 1e-12, 1.0, 1.0)
        assert result >= 0.0
        
        # 非常大的传导度
        result = functions.hcomean(1, 1e12, 1e12, 1.0, 1.0)
        assert result > 0.0


if __name__ == "__main__":
    pytest.main([__file__])
