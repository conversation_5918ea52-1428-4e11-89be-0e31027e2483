#!/usr/bin/env python3
"""
Setup script for PySWAP - High-performance Python wrapper for SWAP model
使用ISO C Binding + Cython方法实现高性能包装器
"""

import os
import sys
import numpy as np
from setuptools import setup, Extension
from Cython.Build import cythonize
from Cython.Compiler import Options

# 启用Cython调试和优化选项
Options.docstrings = True
Options.embed_pos_in_docstring = True

# 根据平台配置编译器标志
if sys.platform.startswith('win'):
    # Windows平台配置
    if 'msvc' in sys.version.lower():
        extra_compile_args = ['/O2', '/openmp', '/fp:fast']
        extra_link_args = ['/openmp']
        libraries = []
    else:
        # MinGW/GCC on Windows
        extra_compile_args = ['-O3', '-fopenmp', '-ffast-math', '-funroll-loops']
        extra_link_args = ['-fopenmp']
        libraries = ['gfortran']
else:
    # Unix-like系统
    extra_compile_args = ['-O3', '-fopenmp', '-ffast-math', '-march=native', '-funroll-loops']
    extra_link_args = ['-fopenmp']
    libraries = ['gfortran']

# 定义Cython扩展模块
extensions = [
    # 核心数学函数模块
    Extension(
        "pyswap.core.functions",
        sources=[
            "pyswap/core/functions.pyx",
            "c_interface/c_functions.c",
        ],
        include_dirs=[
            np.get_include(),
            "c_interface",
            "swap420",
        ],
        libraries=libraries,
        extra_compile_args=extra_compile_args,
        extra_link_args=extra_link_args,
        language="c",
        define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')],
    ),
    
    # 插值计算模块
    Extension(
        "pyswap.core.interpolation",
        sources=[
            "pyswap/core/interpolation.pyx",
            "c_interface/c_interpolation.c",
        ],
        include_dirs=[
            np.get_include(),
            "c_interface",
            "swap420",
        ],
        libraries=libraries,
        extra_compile_args=extra_compile_args,
        extra_link_args=extra_link_args,
        language="c",
        define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')],
    ),
    
    # 土壤水分计算模块
    Extension(
        "pyswap.core.soilwater",
        sources=[
            "pyswap/core/soilwater.pyx",
            "c_interface/c_soilwater.c",
        ],
        include_dirs=[
            np.get_include(),
            "c_interface",
            "swap420",
        ],
        libraries=libraries,
        extra_compile_args=extra_compile_args,
        extra_link_args=extra_link_args,
        language="c",
        define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')],
    ),
    
    # 主SWAP接口模块
    Extension(
        "pyswap.core.swap_interface",
        sources=[
            "pyswap/core/swap_interface.pyx",
            "c_interface/c_swap_interface.c",
        ],
        include_dirs=[
            np.get_include(),
            "c_interface",
            "swap420",
        ],
        libraries=libraries,
        extra_compile_args=extra_compile_args,
        extra_link_args=extra_link_args,
        language="c",
        define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')],
    ),
]

# 使用Cython编译扩展
ext_modules = cythonize(
    extensions,
    compiler_directives={
        'language_level': 3,
        'boundscheck': False,      # 禁用边界检查以提高性能
        'wraparound': False,       # 禁用负索引支持
        'initializedcheck': False, # 禁用初始化检查
        'cdivision': True,         # 使用C除法语义
        'embedsignature': True,    # 嵌入函数签名
        'optimize.use_switch': True,
        'optimize.unpack_method_calls': True,
        'profile': False,          # 生产环境禁用性能分析
    },
    annotate=True,  # 生成HTML注释文件用于优化分析
    nthreads=4,     # 并行编译
)

if __name__ == "__main__":
    setup(
        ext_modules=ext_modules,
        zip_safe=False,
        include_package_data=True,
    )
