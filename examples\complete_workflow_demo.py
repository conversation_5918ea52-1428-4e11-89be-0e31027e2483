#!/usr/bin/env python3
"""
PySWAP完整工作流程演示

展示从模型创建到结果分析的完整SWAP模拟工作流程，
包括数据准备、模型配置、运行和结果处理。
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pyswap
from pyswap.models.swap_model import SwapModel
from pyswap.models.state_manager import SwapState


def create_sample_config():
    """创建示例配置"""
    return {
        # 基本模型参数
        'max_nodes': 50,
        'swkmean': 6,          # 加权调和平均
        'swsophy': 0,          # 标准土壤物理
        
        # 时间控制
        'dt': 0.01,            # 初始时间步长 (天)
        'dtmax': 1.0,          # 最大时间步长 (天)
        'dtmin': 1e-6,         # 最小时间步长 (天)
        'tstart': 0.0,         # 开始时间
        'tend': 30.0,          # 结束时间 (30天)
        
        # 土壤剖面
        'soil_layers': 50,
        'total_depth': 200.0,  # 总深度 (cm)
        
        # 初始条件
        'initial_head': -100.0, # 初始压力水头 (cm)
        
        # 边界条件
        'top_boundary': 'flux',    # 顶部通量边界
        'bottom_boundary': 'free', # 底部自由排水
        
        # 作物参数
        'crop_height': 80.0,       # 作物高度 (cm)
        'root_depth': 60.0,        # 根深 (cm)
        'leaf_area_index': 3.0,    # 叶面积指数
        
        # 气象数据
        'temperature_min': 15.0,   # 最低温度 (°C)
        'temperature_max': 25.0,   # 最高温度 (°C)
        'humidity': 70.0,          # 湿度 (%)
        'wind_speed': 2.0,         # 风速 (m/s)
        'rainfall': 5.0,           # 降雨量 (mm/d)
        'reference_et': 4.0,       # 参考蒸散发 (mm/d)
        'radiation': 20000.0,      # 辐射 (kJ/m²/d)
    }


def setup_soil_profile(config):
    """设置土壤剖面"""
    print("🏗️  设置土壤剖面...")
    
    n_layers = config['soil_layers']
    total_depth = config['total_depth']
    
    # 创建均匀分层
    layer_thickness = total_depth / n_layers
    depths = np.arange(layer_thickness/2, total_depth, layer_thickness)
    
    # 设置初始压力水头 (线性分布)
    initial_heads = np.full(n_layers, config['initial_head'])
    # 添加重力梯度
    for i in range(n_layers):
        initial_heads[i] += depths[i] * 0.1  # 轻微的重力梯度
    
    print(f"   层数: {n_layers}")
    print(f"   总深度: {total_depth} cm")
    print(f"   层厚: {layer_thickness:.1f} cm")
    print(f"   初始水头范围: {initial_heads.min():.1f} ~ {initial_heads.max():.1f} cm")
    
    return depths, initial_heads


def setup_meteorological_data(config, n_days=30):
    """设置气象数据"""
    print("🌤️  设置气象数据...")
    
    # 创建简单的正弦变化气象数据
    days = np.arange(n_days)
    
    meteo_data = {
        'tmin': config['temperature_min'] + 3 * np.sin(2 * np.pi * days / 365),
        'tmax': config['temperature_max'] + 5 * np.sin(2 * np.pi * days / 365),
        'hum': np.full(n_days, config['humidity']),
        'wind': config['wind_speed'] + 0.5 * np.random.normal(0, 1, n_days),
        'rain': np.random.exponential(config['rainfall'], n_days),
        'etref': config['reference_et'] + 1 * np.sin(2 * np.pi * days / 365),
        'rad': config['radiation'] + 5000 * np.sin(2 * np.pi * days / 365),
    }
    
    # 确保物理合理性
    meteo_data['wind'] = np.maximum(meteo_data['wind'], 0.1)
    meteo_data['rain'] = np.maximum(meteo_data['rain'], 0.0)
    meteo_data['etref'] = np.maximum(meteo_data['etref'], 0.1)
    meteo_data['rad'] = np.maximum(meteo_data['rad'], 1000.0)
    
    print(f"   模拟天数: {n_days}")
    print(f"   温度范围: {meteo_data['tmin'].min():.1f} ~ {meteo_data['tmax'].max():.1f} °C")
    print(f"   总降雨量: {meteo_data['rain'].sum():.1f} mm")
    print(f"   平均ETref: {meteo_data['etref'].mean():.1f} mm/d")
    
    return meteo_data


def run_simulation_workflow():
    """运行完整的模拟工作流程"""
    print("\n🚀 开始完整模拟工作流程")
    print("=" * 60)
    
    # 1. 准备配置
    print("1️⃣  准备配置...")
    config = create_sample_config()
    print(f"   配置参数: {len(config)} 项")
    
    # 2. 设置土壤剖面
    depths, initial_heads = setup_soil_profile(config)
    
    # 3. 设置气象数据
    meteo_data = setup_meteorological_data(config, n_days=int(config['tend']))
    
    # 4. 创建和初始化模型
    print("4️⃣  创建和初始化模型...")
    try:
        with SwapModel(max_nodes=config['max_nodes']) as model:
            print("   ✅ 模型创建成功")
            
            # 初始化模型
            model.initialize(config_dict=config)
            print("   ✅ 模型初始化成功")
            
            # 设置土壤剖面
            model.state.soilwater.numnod = len(depths)
            model.state.soilwater.h[:len(depths)] = initial_heads
            model.state.soilwater.dz[:len(depths)] = np.diff(np.concatenate([[0], depths + np.diff(depths)[0]/2]))
            
            print(f"   土壤节点数: {model.state.soilwater.numnod}")
            
            # 5. 运行模拟
            print("5️⃣  运行模拟...")
            results = model.run(start_time=config['tstart'], end_time=config['tend'])
            print("   ✅ 模拟完成")
            
            # 6. 分析结果
            print("6️⃣  分析结果...")
            analyze_results(results, config)
            
            return results
            
    except Exception as e:
        print(f"   ❌ 工作流程失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_results(results, config):
    """分析模拟结果"""
    print("📊 结果分析:")
    
    # 时间信息
    time_info = results['time']
    print(f"   模拟时间: {time_info['current_time']:.2f} 天")
    print(f"   时间步长: {time_info['time_step']:.6f} 天")
    
    # 水量平衡
    water_balance = results['water_balance']
    print(f"   总水量: {water_balance['total_water']:.3f} cm")
    print(f"   水量变化: {water_balance['water_change']:.3f} cm")
    print(f"   顶部通量: {water_balance['top_flux']:.3f} cm/d")
    print(f"   底部通量: {water_balance['bottom_flux']:.3f} cm/d")
    
    # 作物信息
    crop_info = results['crop']
    print(f"   潜在蒸腾: {crop_info['potential_transpiration']:.3f} cm/d")
    print(f"   实际蒸腾: {crop_info['actual_transpiration']:.3f} cm/d")
    print(f"   蒸腾比率: {crop_info['transpiration_ratio']:.3f}")
    
    # 土壤剖面
    soil_profile = results['soil_profile']
    if len(soil_profile['pressure_head']) > 0:
        print(f"   压力水头范围: {soil_profile['pressure_head'].min():.1f} ~ {soil_profile['pressure_head'].max():.1f} cm")
        print(f"   含水量范围: {soil_profile['water_content'].min():.3f} ~ {soil_profile['water_content'].max():.3f}")
        print(f"   传导度范围: {soil_profile['hydraulic_conductivity'].min():.2e} ~ {soil_profile['hydraulic_conductivity'].max():.2e} cm/d")


def save_results(results, output_file="simulation_results.json"):
    """保存结果到文件"""
    print(f"\n💾 保存结果到 {output_file}...")
    
    # 转换numpy数组为列表以便JSON序列化
    def convert_arrays(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_arrays(value) for key, value in obj.items()}
        else:
            return obj
    
    serializable_results = convert_arrays(results)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 结果已保存到 {output_file}")


def create_visualization(results):
    """创建结果可视化"""
    print("\n📈 创建结果可视化...")
    
    try:
        soil_profile = results['soil_profile']
        
        if len(soil_profile['pressure_head']) == 0:
            print("   ⚠️  无土壤剖面数据，跳过可视化")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('SWAP模拟结果', fontsize=16)
        
        # 计算深度
        depths = np.cumsum(np.concatenate([[0], soil_profile['pressure_head']]))[:len(soil_profile['pressure_head'])]
        
        # 压力水头剖面
        axes[0, 0].plot(soil_profile['pressure_head'], depths)
        axes[0, 0].set_xlabel('压力水头 (cm)')
        axes[0, 0].set_ylabel('深度 (cm)')
        axes[0, 0].set_title('压力水头剖面')
        axes[0, 0].invert_yaxis()
        axes[0, 0].grid(True)
        
        # 含水量剖面
        axes[0, 1].plot(soil_profile['water_content'], depths)
        axes[0, 1].set_xlabel('体积含水量 (-)')
        axes[0, 1].set_ylabel('深度 (cm)')
        axes[0, 1].set_title('含水量剖面')
        axes[0, 1].invert_yaxis()
        axes[0, 1].grid(True)
        
        # 水力传导度剖面
        axes[1, 0].semilogx(soil_profile['hydraulic_conductivity'], depths)
        axes[1, 0].set_xlabel('水力传导度 (cm/d)')
        axes[1, 0].set_ylabel('深度 (cm)')
        axes[1, 0].set_title('水力传导度剖面')
        axes[1, 0].invert_yaxis()
        axes[1, 0].grid(True)
        
        # 根系吸水剖面
        axes[1, 1].plot(soil_profile['root_water_uptake'], depths)
        axes[1, 1].set_xlabel('根系吸水 (cm/d)')
        axes[1, 1].set_ylabel('深度 (cm)')
        axes[1, 1].set_title('根系吸水剖面')
        axes[1, 1].invert_yaxis()
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = "swap_simulation_results.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"   ✅ 可视化结果已保存到 {output_file}")
        
        # 显示图片 (如果在交互环境中)
        try:
            plt.show()
        except:
            pass
        
    except ImportError:
        print("   ⚠️  matplotlib不可用，跳过可视化")
    except Exception as e:
        print(f"   ❌ 可视化创建失败: {e}")


def performance_analysis():
    """性能分析"""
    print("\n⚡ 性能分析")
    print("-" * 30)
    
    import time
    
    # 测试不同规模的模型创建时间
    sizes = [10, 20, 50, 100, 200]
    creation_times = []
    
    print("模型创建时间测试:")
    for size in sizes:
        start_time = time.perf_counter()
        
        model = SwapModel(max_nodes=size)
        config = {'max_nodes': size, 'swkmean': 6}
        model.initialize(config_dict=config)
        
        end_time = time.perf_counter()
        elapsed = end_time - start_time
        creation_times.append(elapsed)
        
        print(f"   {size:3d} 节点: {elapsed*1000:6.2f} ms")
    
    # 分析时间复杂度
    if len(creation_times) > 1:
        ratio = creation_times[-1] / creation_times[0]
        size_ratio = sizes[-1] / sizes[0]
        complexity = np.log(ratio) / np.log(size_ratio)
        print(f"   时间复杂度估算: O(n^{complexity:.2f})")


def main():
    """主函数"""
    print("🎯 PySWAP完整工作流程演示")
    print("=" * 60)
    
    # 显示环境信息
    print("📋 环境信息:")
    version_info = pyswap.get_version_info()
    for key, value in version_info.items():
        print(f"   {key}: {value}")
    
    # 检查环境问题
    issues = pyswap.check_environment()
    if issues:
        print("\n⚠️  环境问题:")
        for issue in issues:
            print(f"   - {issue}")
    
    # 运行完整工作流程
    results = run_simulation_workflow()
    
    if results is not None:
        # 保存结果
        save_results(results)
        
        # 创建可视化
        create_visualization(results)
        
        # 性能分析
        performance_analysis()
        
        print("\n🎉 完整工作流程演示成功!")
        print("\n📖 下一步建议:")
        print("   1. 安装Fortran编译器以启用完整数值计算")
        print("   2. 编译Cython扩展模块")
        print("   3. 运行性能基准测试")
        print("   4. 添加实际的SWAP配置文件支持")
        
    else:
        print("\n❌ 工作流程演示失败")
        print("   请检查错误信息并解决问题")


if __name__ == "__main__":
    main()
