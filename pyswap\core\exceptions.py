"""
PySWAP异常类定义

定义了SWAP模型Python包装器中使用的所有异常类，
用于将Fortran错误代码映射到Python异常。
"""

class SwapError(Exception):
    """SWAP模型基础异常类"""
    
    def __init__(self, message, error_code=None, module=None):
        super().__init__(message)
        self.error_code = error_code
        self.module = module
        
    def __str__(self):
        base_msg = super().__str__()
        if self.error_code is not None:
            base_msg += f" (错误代码: {self.error_code})"
        if self.module is not None:
            base_msg += f" [模块: {self.module}]"
        return base_msg


class ConvergenceError(SwapError):
    """数值求解收敛性错误"""
    
    def __init__(self, message, iterations=None, tolerance=None, **kwargs):
        super().__init__(message, **kwargs)
        self.iterations = iterations
        self.tolerance = tolerance
        
    def __str__(self):
        base_msg = super().__str__()
        if self.iterations is not None:
            base_msg += f" (迭代次数: {self.iterations})"
        if self.tolerance is not None:
            base_msg += f" (容差: {self.tolerance})"
        return base_msg


class BoundaryError(SwapError):
    """边界条件错误"""
    
    def __init__(self, message, boundary_type=None, **kwargs):
        super().__init__(message, **kwargs)
        self.boundary_type = boundary_type
        
    def __str__(self):
        base_msg = super().__str__()
        if self.boundary_type is not None:
            base_msg += f" (边界类型: {self.boundary_type})"
        return base_msg


class InitializationError(SwapError):
    """模型初始化错误"""
    pass


class ConfigurationError(SwapError):
    """配置文件错误"""
    
    def __init__(self, message, config_file=None, line_number=None, **kwargs):
        super().__init__(message, **kwargs)
        self.config_file = config_file
        self.line_number = line_number
        
    def __str__(self):
        base_msg = super().__str__()
        if self.config_file is not None:
            base_msg += f" (文件: {self.config_file})"
        if self.line_number is not None:
            base_msg += f" (行号: {self.line_number})"
        return base_msg


class MemoryError(SwapError):
    """内存分配错误"""
    pass


class TimeStepError(SwapError):
    """时间步长错误"""
    
    def __init__(self, message, time_step=None, **kwargs):
        super().__init__(message, **kwargs)
        self.time_step = time_step
        
    def __str__(self):
        base_msg = super().__str__()
        if self.time_step is not None:
            base_msg += f" (时间步长: {self.time_step})"
        return base_msg


class MassBalanceError(SwapError):
    """质量平衡错误"""
    
    def __init__(self, message, balance_error=None, **kwargs):
        super().__init__(message, **kwargs)
        self.balance_error = balance_error
        
    def __str__(self):
        base_msg = super().__str__()
        if self.balance_error is not None:
            base_msg += f" (平衡误差: {self.balance_error})"
        return base_msg


# 错误代码到异常类的映射
ERROR_CODE_MAP = {
    1: InitializationError,
    2: ConfigurationError,
    3: ConvergenceError,
    4: BoundaryError,
    5: MemoryError,
    6: TimeStepError,
    7: MassBalanceError,
}


def map_fortran_error(error_code, message="未知错误", **kwargs):
    """
    将Fortran错误代码映射到相应的Python异常
    
    Parameters:
    -----------
    error_code : int
        Fortran模块返回的错误代码
    message : str
        错误消息
    **kwargs : dict
        传递给异常构造函数的额外参数
        
    Returns:
    --------
    Exception
        相应的异常实例
    """
    exception_class = ERROR_CODE_MAP.get(error_code, SwapError)
    return exception_class(message, error_code=error_code, **kwargs)


def check_error_code(error_code, context="SWAP计算"):
    """
    检查错误代码并在必要时抛出异常
    
    Parameters:
    -----------
    error_code : int
        要检查的错误代码
    context : str
        错误发生的上下文
    """
    if error_code != 0:
        raise map_fortran_error(error_code, f"{context}失败", module=context)
