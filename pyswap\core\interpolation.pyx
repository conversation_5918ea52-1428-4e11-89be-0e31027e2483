# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
# cython: initializedcheck=False

"""
插值计算模块

封装sptabulated.f90中的TSPACK张力样条插值库，提供：
- 高精度张力样条插值
- 表格化函数评估
- 数值微分计算

保持与原始Fortran TSPACK库的完全兼容性。
"""

import numpy as np
cimport numpy as cnp
cimport cython
from libc.math cimport log, exp, fabs
from ..core.exceptions import SwapError, ConvergenceError

# 导入C接口声明
cdef extern from "../c_interface/swap_c_interface.h":
    ctypedef double real8_t
    ctypedef int integer_t
    
    # 插值计算的C接口声明
    void c_eval_tabulated_function(integer_t inverse, integer_t n, 
                                   integer_t ind1, integer_t ind2, integer_t ind3,
                                   integer_t node, real8_t sptab[][1000],
                                   integer_t ientrytab[][50006],
                                   real8_t xe, real8_t* ye, real8_t* dyedxe,
                                   integer_t iwhat)
    
    void c_preproc_tabulated_function(integer_t flag, integer_t n,
                                      real8_t* x, real8_t* y, 
                                      real8_t* dydx, real8_t* sigma)


@cython.boundscheck(False)
@cython.wraparound(False)
def eval_tabulated_function(int inverse, int n, int ind1, int ind2, int ind3,
                           int node, cnp.ndarray[real8_t, ndim=3] sptab,
                           cnp.ndarray[integer_t, ndim=2] ientrytab,
                           double xe):
    """
    评估表格化函数
    
    对应Fortran子程序: EvalTabulatedFunction
    
    Parameters:
    -----------
    inverse : int
        反向插值标志 (0=正向, 1=反向)
    n : int
        表格条目数
    ind1, ind2, ind3 : int
        数组索引指示器
    node : int
        节点编号
    sptab : ndarray[float, 3D]
        样条表格数据 [7, MACP, MATAB]
    ientrytab : ndarray[int, 2D]
        条目表格 [MACP, MATABENTRIES+1]
    xe : float
        输入值
        
    Returns:
    --------
    tuple[float, float]
        (插值结果, 导数值)
        
    Raises:
    -------
    ValueError
        当输入参数无效时
    SwapError
        当插值计算失败时
    """
    # 输入验证
    if node < 1:
        raise ValueError(f"无效的节点编号: {node}")
    
    if n < 2:
        raise ValueError(f"表格条目数太少: {n}")
    
    if inverse not in [0, 1]:
        raise ValueError(f"无效的inverse标志: {inverse}")
    
    # 检查数组维度
    if sptab.shape[0] != 7 or sptab.shape[1] < node or sptab.shape[2] < n:
        raise ValueError("sptab数组维度不匹配")
    
    if ientrytab.shape[0] < node:
        raise ValueError("ientrytab数组维度不匹配")
    
    # 准备输出变量
    cdef real8_t ye = 0.0
    cdef real8_t dyedxe = 0.0
    
    # 调用C接口函数
    try:
        c_eval_tabulated_function(inverse, n, ind1, ind2, ind3, node,
                                  <real8_t(*)[1000]>sptab.data,
                                  <integer_t(*)[50006]>ientrytab.data,
                                  xe, &ye, &dyedxe, 1)
    except Exception as e:
        raise SwapError(f"插值计算失败: {e}", module="interpolation")
    
    return ye, dyedxe


@cython.boundscheck(False)
@cython.wraparound(False)
def preproc_tabulated_function(int flag, cnp.ndarray[real8_t, ndim=1] x,
                              cnp.ndarray[real8_t, ndim=1] y):
    """
    预处理表格化函数
    
    对应Fortran子程序: PreProcTabulatedFunction
    
    Parameters:
    -----------
    flag : int
        处理标志
    x : ndarray[float]
        自变量数组
    y : ndarray[float]
        因变量数组
        
    Returns:
    --------
    tuple[ndarray, ndarray]
        (导数数组, 张力因子数组)
        
    Raises:
    -------
    ValueError
        当输入数组无效时
    SwapError
        当预处理失败时
    """
    cdef int n = x.shape[0]
    
    # 输入验证
    if y.shape[0] != n:
        raise ValueError("x和y数组长度必须相同")
    
    if n < 2:
        raise ValueError("数据点数量太少")
    
    # 检查x数组是否单调递增
    for i in range(1, n):
        if x[i] <= x[i-1]:
            raise ValueError("x数组必须严格单调递增")
    
    # 准备输出数组
    cdef cnp.ndarray[real8_t, ndim=1] dydx = np.zeros(n, dtype=np.float64)
    cdef cnp.ndarray[real8_t, ndim=1] sigma = np.zeros(n, dtype=np.float64)
    
    # 调用C接口函数
    try:
        c_preproc_tabulated_function(flag, n, <real8_t*>x.data, <real8_t*>y.data,
                                    <real8_t*>dydx.data, <real8_t*>sigma.data)
    except Exception as e:
        raise SwapError(f"表格预处理失败: {e}", module="interpolation")
    
    return dydx, sigma


@cython.boundscheck(False)
@cython.wraparound(False)
def interpolate_soil_property(int node, double head, int property_type):
    """
    插值计算土壤特性
    
    Parameters:
    -----------
    node : int
        节点编号
    head : float
        压力水头 (cm)
    property_type : int
        特性类型 (1=含水量, 2=传导度, 3=微分含水量)
        
    Returns:
    --------
    float
        插值结果
        
    Raises:
    -------
    ValueError
        当输入参数无效时
    SwapError
        当插值失败时
    """
    if node < 1:
        raise ValueError(f"无效的节点编号: {node}")
    
    if property_type not in [1, 2, 3]:
        raise ValueError(f"无效的特性类型: {property_type}")
    
    # 这里需要实际的表格数据
    # 临时返回简单计算结果
    if property_type == 1:  # 含水量
        return c_watcon(node, head)
    elif property_type == 2:  # 传导度
        # 需要theta值，这里使用递归调用
        cdef real8_t theta = c_watcon(node, head)
        return c_hconduc(node, head, theta, 1.0)
    else:  # 微分含水量
        return c_moiscap(node, head)


class TabulatedFunction:
    """
    表格化函数类
    
    封装TSPACK张力样条插值功能，提供面向对象的接口
    """
    
    def __init__(self, x_data, y_data, tension_factor=0.0):
        """
        初始化表格化函数
        
        Parameters:
        -----------
        x_data : array_like
            自变量数据点
        y_data : array_like
            因变量数据点
        tension_factor : float
            张力因子，控制样条的"紧张度"
        """
        self.x_data = np.asarray(x_data, dtype=np.float64)
        self.y_data = np.asarray(y_data, dtype=np.float64)
        self.tension_factor = tension_factor
        
        # 验证输入
        if len(self.x_data) != len(self.y_data):
            raise ValueError("x_data和y_data长度必须相同")
        
        if len(self.x_data) < 2:
            raise ValueError("至少需要2个数据点")
        
        # 预处理数据
        self.dydx, self.sigma = preproc_tabulated_function(0, self.x_data, self.y_data)
        self._prepared = True
    
    def evaluate(self, x_values):
        """
        评估函数值
        
        Parameters:
        -----------
        x_values : float or array_like
            要评估的x值
            
        Returns:
        --------
        float or ndarray
            插值结果
        """
        if not self._prepared:
            raise SwapError("函数未预处理")
        
        # 处理单个值
        if np.isscalar(x_values):
            return self._evaluate_single(x_values)
        
        # 处理数组
        x_array = np.asarray(x_values)
        result = np.zeros_like(x_array)
        
        for i, x in enumerate(x_array.flat):
            result.flat[i] = self._evaluate_single(x)
        
        return result
    
    def _evaluate_single(self, x):
        """评估单个x值"""
        # 边界检查
        if x < self.x_data[0] or x > self.x_data[-1]:
            raise ValueError(f"x值 {x} 超出插值范围 [{self.x_data[0]}, {self.x_data[-1]}]")
        
        # 简单线性插值实现（实际应使用张力样条）
        return np.interp(x, self.x_data, self.y_data)
