# SWAP 4.2.0 土壤-水分-大气-植物模型深入学习计划

## 学习目标明确化

### 请根据您的背景选择适合的学习路径：

#### 🎯 **学习者背景分类**
1. **Python开发者** → 目标：理解模型原理，参与PySWAP开发
2. **土壤科学研究者** → 目标：深入理解模型机制，改进算法
3. **数值建模初学者** → 目标：全面学习建模方法，独立开发能力
4. **环境工程师** → 目标：应用模型解决实际问题
5. **计算机科学学生** → 目标：学习科学计算软件架构设计

#### 🎯 **最终目标分类**
- **A类目标**：理解模型原理，能够使用和配置SWAP模型
- **B类目标**：参与PySWAP开发，能够封装和优化代码
- **C类目标**：独立开发类似模型，掌握完整建模流程
- **D类目标**：研究改进算法，发表学术论文

---

## 通用学习路径（适用于所有背景）

### 📚 **第一阶段：基础理论建立（2-4周）**

#### 学习内容
1. **土壤物理学基础**
   - 土壤水分特征曲线（θ-h关系）
   - 导水率函数（K-h关系）
   - Richards方程的物理意义
   - 土壤分层概念和参数化

2. **数值方法基础**
   - 有限差分方法原理
   - 三对角矩阵求解（Thomas算法）
   - 插值方法（线性、样条插值）
   - 时间步进方案

3. **SWAP模型概述**
   - 模型发展历史和应用领域
   - 主要物理过程和数学描述
   - 模型输入输出结构

#### 推荐学习资源
- **教材**：
  - "Soil Physics" by Daniel Hillel（土壤物理学经典教材）
  - "Numerical Methods for Engineers" by Chapra & Canale
- **论文**：
  - van Dam et al. (2008) "Theory of SWAP version 3.2"
  - Kroes et al. (2017) "SWAP version 4: Theory description"
- **在线资源**：
  - SWAP官方文档：https://www.swap.alterra.nl/
  - Wageningen UR的SWAP教程

#### 实践练习
1. 手工计算简单的土壤水分平衡
2. 用Excel实现简单的有限差分求解
3. 阅读SWAP用户手册，理解参数含义

#### 学习验证
- [ ] 能够解释土壤水分特征曲线的物理意义
- [ ] 理解Richards方程的数值求解原理
- [ ] 熟悉SWAP模型的主要组成部分

---

### 🔧 **第二阶段：技术栈掌握（3-6周）**

#### 根据背景选择技术重点：

##### 对于Python开发者：
**重点：Fortran语言基础 + 数值计算**
- Fortran 90/95语法基础
- MODULE系统和接口设计
- 数组操作和内存管理
- f2py封装技术

**学习资源**：
- "Modern Fortran Explained" by Metcalf et al.
- NumPy/SciPy文档中的f2py部分
- Intel Fortran编译器文档

**实践项目**：
```fortran
! 实现简单的插值函数
module simple_interpolation
    implicit none
contains
    function linear_interp(x, y, xi) result(yi)
        real(8), intent(in) :: x(:), y(:), xi
        real(8) :: yi
        ! 实现线性插值算法
    end function
end module
```

##### 对于土壤科学研究者：
**重点：数值方法 + 模型物理过程**
- 偏微分方程数值解法
- 土壤水力参数确定方法
- 模型校准和验证技术
- 敏感性分析方法

**学习资源**：
- "Vadose Zone Hydrology" by Jury & Horton
- "Parameter Estimation and Inverse Problems" by Aster et al.
- HYDRUS软件教程（类似模型）

**实践项目**：
- 分析不同土壤类型的水力参数
- 比较不同插值方法的精度
- 设计模型验证实验

##### 对于数值建模初学者：
**重点：全面基础 + 软件工程**
- 编程语言基础（Fortran或Python）
- 软件架构设计原理
- 版本控制和协作开发
- 测试驱动开发

**学习资源**：
- "Scientific Computing with Python" by Johansson
- "Clean Code" by Robert Martin
- Git和GitHub使用教程

**实践项目**：
- 从零实现1D土壤水分模型
- 设计模块化的代码架构
- 编写单元测试

#### 通用技术技能
1. **开发环境搭建**
   - Intel Fortran或gfortran编译器
   - Python科学计算环境（Anaconda）
   - 代码编辑器（VS Code + Fortran插件）
   - 调试工具使用

2. **版本控制**
   - Git基础操作
   - GitHub协作流程
   - 代码审查方法

#### 学习验证
- [ ] 能够编译和运行SWAP模型
- [ ] 理解模块间的依赖关系
- [ ] 能够修改简单的模型参数

---

### 🧮 **第三阶段：核心模块深入（4-8周）**

#### 按优先级学习核心模块：

##### 优先级1：插值计算核心（sptabulated.f90）
**学习重点**：
- 张力样条插值算法原理
- TSPACK库的数学基础
- 对数变换的作用和实现
- 表格化数据的存储结构

**深入内容**：
```fortran
! 理解关键数据结构
real(8) :: sptab(7,macp,matab)
! sptab(1,:,:) = 压力水头 h
! sptab(2,:,:) = 含水量 θ  
! sptab(3,:,:) = 导水率 K
! sptab(4,:,:) = dθ/dh
! sptab(5,:,:) = dK/dh
! sptab(6,:,:) = σ(θ)
! sptab(7,:,:) = σ(K)
```

**实践练习**：
1. 手工实现简单的样条插值
2. 分析不同张力因子对插值精度的影响
3. 比较线性插值vs样条插值的性能

**学习资源**：
- "A Practical Guide to Splines" by de Boor
- TSPACK原始论文：Renka (1987)
- 数值分析教材中的插值章节

##### 优先级2：土壤水力函数（functions.f90）
**学习重点**：
- van Genuchten-Mualem模型
- 水力传导度计算方法
- 比水容量的数值计算
- 边界条件处理

**关键函数分析**：
```fortran
! 核心函数理解
real(8) function watcon(node, head)     ! θ(h)
real(8) function hconduc(node, head, theta, rfcp)  ! K(h)
real(8) function moiscap(node, head)    ! C(h) = dθ/dh
real(8) function dhconduc(node, head)   ! dK/dh
```

**实践练习**：
1. 绘制不同土壤的θ-h和K-h曲线
2. 分析参数敏感性
3. 实现简化版的水力函数

##### 优先级3：数值求解器（soilwater.f90 + tridag.f90）
**学习重点**：
- Richards方程的离散化
- 三对角矩阵系统的构建
- 时间步长自适应控制
- 收敛性判断标准

**数学基础**：
```
∂θ/∂t = ∂/∂z[K(h)(∂h/∂z + 1)] - S(h)
```

**实践练习**：
1. 手工推导离散化方程
2. 实现简单的Thomas算法
3. 分析数值稳定性条件

#### 学习验证
- [ ] 能够解释插值算法的数学原理
- [ ] 理解土壤水力参数的物理意义
- [ ] 能够分析数值求解的收敛性

---

### 🌱 **第四阶段：完整系统理解（6-10周）**

#### 系统集成学习
1. **模型初始化流程**（initialize.f90）
2. **边界条件处理**（boundtop.f90, boundbottom.f90）
3. **作物生长模拟**（cropgrowth.f90）
4. **大孔隙流处理**（macropore.f90）
5. **输入输出管理**（readswap.f90, swapoutput.f90）

#### 高级主题
1. **多物理场耦合**
   - 水分-热量传输耦合
   - 水分-溶质传输耦合
   - 土壤-植物-大气连续体

2. **数值方法优化**
   - 自适应网格技术
   - 并行计算实现
   - 内存优化策略

3. **模型校准与验证**
   - 参数优化算法
   - 不确定性分析
   - 敏感性分析

#### 综合项目
根据学习目标选择：

**A类目标项目**：配置SWAP模型模拟特定场地
**B类目标项目**：实现关键模块的Python封装
**C类目标项目**：开发简化版的1D土壤水分模型
**D类目标项目**：改进某个算法并验证性能

#### 学习验证
- [ ] 能够独立运行完整的SWAP模拟
- [ ] 理解各模块间的数据流和控制流
- [ ] 能够诊断和解决常见的数值问题

---

## 学习资源汇总

### 📖 **核心教材**
1. **土壤物理学**：
   - Hillel, D. "Introduction to Soil Physics"
   - Jury, W.A. & Horton, R. "Soil Physics"

2. **数值方法**：
   - Chapra, S.C. "Numerical Methods for Engineers"
   - Press, W.H. et al. "Numerical Recipes"

3. **Fortran编程**：
   - Metcalf, M. et al. "Modern Fortran Explained"
   - Chapman, S.J. "Fortran 95/2003 for Scientists and Engineers"

### 📄 **关键论文**
1. van Genuchten, M.Th. (1980). "A closed-form equation for predicting the hydraulic conductivity of unsaturated soils"
2. Kroes, J.G. et al. (2017). "SWAP version 4: Theory description and user manual"
3. Renka, R.J. (1987). "Interpolatory tension splines with automatic selection of tension factors"

### 🌐 **在线资源**
1. SWAP官方网站：https://www.swap.alterra.nl/
2. Wageningen UR模型文档
3. HYDRUS软件教程（参考模型）
4. NumPy/SciPy科学计算文档

### 🛠️ **开发工具**
1. **编译器**：Intel Fortran Compiler / gfortran
2. **IDE**：VS Code + Fortran插件 / Code::Blocks
3. **Python环境**：Anaconda + Jupyter Notebook
4. **版本控制**：Git + GitHub
5. **调试工具**：gdb / Intel Inspector

---

## 学习进度跟踪

### 阶段性检查点
- [ ] **第1周末**：完成土壤物理学基础概念学习
- [ ] **第2周末**：理解Richards方程和数值方法
- [ ] **第4周末**：掌握基本的Fortran编程技能
- [ ] **第6周末**：能够编译运行SWAP模型
- [ ] **第8周末**：深入理解sptabulated.f90模块
- [ ] **第12周末**：完成核心模块的学习
- [ ] **第16周末**：能够进行模型修改和扩展

### 学习成果展示
1. **技术博客**：记录学习过程和心得
2. **代码仓库**：上传练习项目和改进代码
3. **学术报告**：总结模型原理和应用
4. **开源贡献**：参与PySWAP项目开发

---

## 常见问题与解决方案

### Q1: Fortran代码难以理解怎么办？
**解决方案**：
- 从简单的子程序开始，逐步理解复杂模块
- 使用代码注释和流程图辅助理解
- 参考现代Fortran教材，理解语言特性

### Q2: 数学公式推导困难？
**解决方案**：
- 重点理解物理意义，而非数学细节
- 使用数值实验验证理论推导
- 寻找相关的教学视频和在线课程

### Q3: 如何验证学习效果？
**解决方案**：
- 设计小型测试案例验证理解
- 与标准结果对比验证实现正确性
- 参与开源项目获得同行反馈

### Q4: 学习时间安排建议？
**解决方案**：
- 每天1-2小时持续学习比集中突击更有效
- 理论学习与实践编程相结合
- 定期回顾和总结，巩固学习成果

---

## 实践指导与代码示例

### 🔬 **第一个实践项目：理解插值算法**

#### 项目目标
通过实现简化版的张力样条插值，理解sptabulated.f90的核心算法。

#### 实现步骤

##### 步骤1：线性插值基础实现
```python
import numpy as np
import matplotlib.pyplot as plt

def linear_interpolation(x_data, y_data, x_query):
    """
    简单线性插值实现
    """
    # 找到插值区间
    idx = np.searchsorted(x_data, x_query) - 1
    idx = np.clip(idx, 0, len(x_data) - 2)

    # 线性插值计算
    x1, x2 = x_data[idx], x_data[idx + 1]
    y1, y2 = y_data[idx], y_data[idx + 1]

    return y1 + (y2 - y1) * (x_query - x1) / (x2 - x1)

# 测试数据：模拟土壤水分特征曲线
h_data = np.array([-1000, -500, -100, -50, -10, -1, 0])  # 压力水头 (cm)
theta_data = np.array([0.1, 0.15, 0.25, 0.35, 0.45, 0.48, 0.5])  # 含水量

# 插值测试
h_query = np.linspace(-1000, 0, 100)
theta_interp = [linear_interpolation(h_data, theta_data, h) for h in h_query]

# 可视化结果
plt.figure(figsize=(10, 6))
plt.plot(h_data, theta_data, 'ro', label='原始数据点')
plt.plot(h_query, theta_interp, 'b-', label='线性插值')
plt.xlabel('压力水头 h (cm)')
plt.ylabel('含水量 θ (cm³/cm³)')
plt.title('土壤水分特征曲线插值')
plt.legend()
plt.grid(True)
plt.show()
```

##### 步骤2：样条插值改进
```python
from scipy.interpolate import CubicSpline, PchipInterpolator

def compare_interpolation_methods(h_data, theta_data):
    """
    比较不同插值方法的效果
    """
    h_query = np.linspace(h_data[0], h_data[-1], 200)

    # 线性插值
    theta_linear = np.interp(h_query, h_data, theta_data)

    # 三次样条插值
    cs = CubicSpline(h_data, theta_data)
    theta_cubic = cs(h_query)

    # PCHIP插值（保形插值，类似SWAP中的方法）
    pchip = PchipInterpolator(h_data, theta_data)
    theta_pchip = pchip(h_query)

    # 可视化比较
    plt.figure(figsize=(12, 8))
    plt.plot(h_data, theta_data, 'ko', markersize=8, label='原始数据')
    plt.plot(h_query, theta_linear, 'r-', label='线性插值')
    plt.plot(h_query, theta_cubic, 'g-', label='三次样条')
    plt.plot(h_query, theta_pchip, 'b-', label='PCHIP插值')

    plt.xlabel('压力水头 h (cm)')
    plt.ylabel('含水量 θ (cm³/cm³)')
    plt.title('不同插值方法比较')
    plt.legend()
    plt.grid(True)
    plt.show()

    return h_query, theta_linear, theta_cubic, theta_pchip

# 执行比较
h_query, theta_linear, theta_cubic, theta_pchip = compare_interpolation_methods(h_data, theta_data)
```

##### 步骤3：理解对数变换
```python
def log_transform_interpolation(h_data, k_data):
    """
    模拟SWAP中的对数变换插值（用于导水率）
    """
    # 对数变换（避免负值和零值）
    h_transform = -h_data + 1.0  # 变换压力水头
    k_log = np.log(k_data + 1e-10)  # 对数变换导水率

    # 在变换空间中插值
    h_query_transform = np.linspace(h_transform[0], h_transform[-1], 100)
    k_log_interp = np.interp(h_query_transform, h_transform, k_log)

    # 反变换
    h_query = -(h_query_transform - 1.0)
    k_interp = np.exp(k_log_interp)

    return h_query, k_interp

# 测试导水率插值
k_data = np.array([1e-8, 1e-6, 1e-4, 1e-2, 1e-1, 1.0, 10.0])  # 导水率 (cm/d)
h_query, k_interp = log_transform_interpolation(h_data, k_data)

plt.figure(figsize=(10, 6))
plt.semilogy(h_data, k_data, 'ro', label='原始数据')
plt.semilogy(h_query, k_interp, 'b-', label='对数变换插值')
plt.xlabel('压力水头 h (cm)')
plt.ylabel('导水率 K (cm/d)')
plt.title('导水率函数插值（对数尺度）')
plt.legend()
plt.grid(True)
plt.show()
```

### 🧪 **第二个实践项目：简化的Richards方程求解**

#### 项目目标
实现1D Richards方程的有限差分求解，理解soilwater.f90的数值方法。

#### 核心实现
```python
import numpy as np
from scipy.sparse import diags
from scipy.sparse.linalg import spsolve

class SimpleRichardsEquation:
    """
    简化的1D Richards方程求解器
    """

    def __init__(self, z_nodes, soil_params):
        self.z = np.array(z_nodes)  # 深度节点 (cm)
        self.nz = len(z_nodes)
        self.dz = np.diff(z_nodes)  # 层厚度
        self.soil_params = soil_params

    def van_genuchten_theta(self, h):
        """van Genuchten水分特征曲线"""
        alpha, n, theta_r, theta_s = self.soil_params
        m = 1 - 1/n

        # 避免数值问题
        h = np.minimum(h, -1e-6)

        Se = (1 + (alpha * abs(h))**n)**(-m)
        theta = theta_r + (theta_s - theta_r) * Se
        return theta

    def van_genuchten_K(self, h):
        """van Genuchten导水率函数"""
        alpha, n, theta_r, theta_s, Ks = self.soil_params
        m = 1 - 1/n

        h = np.minimum(h, -1e-6)
        Se = (1 + (alpha * abs(h))**n)**(-m)
        K = Ks * Se**0.5 * (1 - (1 - Se**(1/m))**m)**2
        return K

    def capacity(self, h):
        """比水容量 C = dθ/dh"""
        alpha, n, theta_r, theta_s = self.soil_params
        m = 1 - 1/n

        h = np.minimum(h, -1e-6)
        term1 = (alpha * abs(h))**(n-1)
        term2 = (1 + (alpha * abs(h))**n)**(-m-1)
        C = alpha * n * m * (theta_s - theta_r) * term1 * term2
        return C

    def solve_timestep(self, h_old, dt, boundary_conditions):
        """
        求解单个时间步
        """
        # 构建系数矩阵（三对角）
        A = np.zeros((self.nz, self.nz))
        b = np.zeros(self.nz)

        for i in range(1, self.nz-1):
            # 节点间的导水率（调和平均）
            K_up = 2 * self.van_genuchten_K(h_old[i-1]) * self.van_genuchten_K(h_old[i]) / \
                   (self.van_genuchten_K(h_old[i-1]) + self.van_genuchten_K(h_old[i]) + 1e-10)
            K_down = 2 * self.van_genuchten_K(h_old[i]) * self.van_genuchten_K(h_old[i+1]) / \
                     (self.van_genuchten_K(h_old[i]) + self.van_genuchten_K(h_old[i+1]) + 1e-10)

            # 比水容量
            C = self.capacity(h_old[i])

            # 有限差分系数
            dz_up = self.z[i] - self.z[i-1]
            dz_down = self.z[i+1] - self.z[i]

            # 对角线元素
            A[i, i] = C/dt + K_up/dz_up**2 + K_down/dz_down**2

            # 上下对角线元素
            A[i, i-1] = -K_up/dz_up**2
            A[i, i+1] = -K_down/dz_down**2

            # 右端项
            b[i] = C * h_old[i] / dt + (K_down - K_up) / (dz_up + dz_down)

        # 边界条件
        A[0, 0] = 1.0
        b[0] = boundary_conditions['top']
        A[-1, -1] = 1.0
        b[-1] = boundary_conditions['bottom']

        # 求解线性系统
        h_new = spsolve(A, b)
        return h_new

# 使用示例
if __name__ == "__main__":
    # 土壤参数（砂土）
    alpha = 0.145  # cm^-1
    n = 2.68
    theta_r = 0.045
    theta_s = 0.43
    Ks = 712.8  # cm/d
    soil_params = (alpha, n, theta_r, theta_s, Ks)

    # 网格设置
    z_nodes = np.linspace(0, -100, 21)  # 0到-100cm，21个节点

    # 创建求解器
    solver = SimpleRichardsEquation(z_nodes, soil_params)

    # 初始条件
    h_initial = np.full(len(z_nodes), -50.0)  # 初始压力水头 -50cm

    # 边界条件
    boundary_conditions = {
        'top': -10.0,    # 顶部压力水头 -10cm
        'bottom': -100.0  # 底部压力水头 -100cm
    }

    # 时间步进
    dt = 0.1  # 时间步长 (天)
    t_end = 10.0  # 总时间 (天)
    n_steps = int(t_end / dt)

    # 存储结果
    h_history = [h_initial.copy()]

    h_current = h_initial.copy()
    for step in range(n_steps):
        h_current = solver.solve_timestep(h_current, dt, boundary_conditions)
        h_history.append(h_current.copy())

    # 可视化结果
    plt.figure(figsize=(12, 8))

    # 绘制不同时间的压力水头剖面
    times_to_plot = [0, 2, 5, 10]
    for t in times_to_plot:
        step_idx = int(t / dt)
        if step_idx < len(h_history):
            plt.plot(h_history[step_idx], z_nodes,
                    label=f't = {t} 天', linewidth=2)

    plt.xlabel('压力水头 h (cm)')
    plt.ylabel('深度 z (cm)')
    plt.title('1D Richards方程求解结果')
    plt.legend()
    plt.grid(True)
    plt.show()

    # 绘制含水量剖面
    plt.figure(figsize=(12, 8))
    for t in times_to_plot:
        step_idx = int(t / dt)
        if step_idx < len(h_history):
            theta = solver.van_genuchten_theta(h_history[step_idx])
            plt.plot(theta, z_nodes,
                    label=f't = {t} 天', linewidth=2)

    plt.xlabel('含水量 θ (cm³/cm³)')
    plt.ylabel('深度 z (cm)')
    plt.title('含水量剖面演化')
    plt.legend()
    plt.grid(True)
    plt.show()
```

### 📊 **第三个实践项目：SWAP模块分析工具**

#### 项目目标
开发工具来分析SWAP源码结构，理解模块间的依赖关系。

#### 实现代码
```python
import os
import re
import networkx as nx
import matplotlib.pyplot as plt
from collections import defaultdict

class SWAPCodeAnalyzer:
    """
    SWAP源码分析工具
    """

    def __init__(self, swap_directory):
        self.swap_dir = swap_directory
        self.modules = {}
        self.dependencies = defaultdict(list)
        self.functions = defaultdict(list)

    def analyze_file(self, filepath):
        """分析单个Fortran文件"""
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        filename = os.path.basename(filepath)

        # 查找模块定义
        module_matches = re.findall(r'^\s*module\s+(\w+)', content, re.MULTILINE | re.IGNORECASE)
        if module_matches:
            self.modules[filename] = module_matches

        # 查找use语句
        use_matches = re.findall(r'^\s*use\s+(\w+)', content, re.MULTILINE | re.IGNORECASE)
        if use_matches:
            self.dependencies[filename] = list(set(use_matches))

        # 查找函数和子程序
        func_matches = re.findall(r'^\s*(?:real\(8\)|integer|logical|character.*?)\s+function\s+(\w+)',
                                 content, re.MULTILINE | re.IGNORECASE)
        sub_matches = re.findall(r'^\s*subroutine\s+(\w+)', content, re.MULTILINE | re.IGNORECASE)

        all_routines = func_matches + sub_matches
        if all_routines:
            self.functions[filename] = all_routines

    def analyze_all_files(self):
        """分析所有Fortran文件"""
        for filename in os.listdir(self.swap_dir):
            if filename.endswith(('.f90', '.fi')):
                filepath = os.path.join(self.swap_dir, filename)
                self.analyze_file(filepath)

    def create_dependency_graph(self):
        """创建依赖关系图"""
        G = nx.DiGraph()

        # 添加节点
        for filename in self.dependencies.keys():
            G.add_node(filename)

        # 添加边（依赖关系）
        for filename, deps in self.dependencies.items():
            for dep in deps:
                # 查找提供该模块的文件
                provider = None
                for file, modules in self.modules.items():
                    if dep.lower() in [m.lower() for m in modules]:
                        provider = file
                        break

                if provider:
                    G.add_edge(filename, provider, label=dep)

        return G

    def visualize_dependencies(self, save_path=None):
        """可视化依赖关系"""
        G = self.create_dependency_graph()

        plt.figure(figsize=(16, 12))

        # 使用层次布局
        try:
            pos = nx.spring_layout(G, k=3, iterations=50)
        except:
            pos = nx.random_layout(G)

        # 绘制节点
        nx.draw_networkx_nodes(G, pos, node_color='lightblue',
                              node_size=3000, alpha=0.7)

        # 绘制边
        nx.draw_networkx_edges(G, pos, edge_color='gray',
                              arrows=True, arrowsize=20, alpha=0.5)

        # 绘制标签
        labels = {node: node.replace('.f90', '').replace('.fi', '')
                 for node in G.nodes()}
        nx.draw_networkx_labels(G, pos, labels, font_size=8)

        plt.title('SWAP模块依赖关系图', fontsize=16)
        plt.axis('off')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def generate_report(self):
        """生成分析报告"""
        print("=== SWAP源码分析报告 ===\n")

        print(f"1. 文件统计:")
        print(f"   总文件数: {len(self.modules) + len(self.dependencies)}")
        print(f"   定义模块的文件: {len(self.modules)}")
        print(f"   有依赖关系的文件: {len(self.dependencies)}")
        print()

        print("2. 模块定义:")
        for filename, modules in sorted(self.modules.items()):
            print(f"   {filename}: {modules}")
        print()

        print("3. 最常被依赖的模块:")
        module_usage = defaultdict(int)
        for deps in self.dependencies.values():
            for dep in deps:
                module_usage[dep] += 1

        sorted_usage = sorted(module_usage.items(), key=lambda x: x[1], reverse=True)
        for module, count in sorted_usage[:10]:
            print(f"   {module}: 被引用 {count} 次")
        print()

        print("4. 函数/子程序统计:")
        total_routines = sum(len(routines) for routines in self.functions.values())
        print(f"   总函数/子程序数: {total_routines}")

        # 显示函数最多的文件
        sorted_files = sorted(self.functions.items(), key=lambda x: len(x[1]), reverse=True)
        print("   函数最多的文件:")
        for filename, routines in sorted_files[:5]:
            print(f"     {filename}: {len(routines)} 个函数/子程序")

# 使用示例
if __name__ == "__main__":
    # 分析SWAP源码
    analyzer = SWAPCodeAnalyzer('swap420')  # 替换为实际路径
    analyzer.analyze_all_files()

    # 生成报告
    analyzer.generate_report()

    # 可视化依赖关系
    analyzer.visualize_dependencies('swap_dependencies.png')
```

### 🎯 **学习成果检验项目**

#### 综合项目：简化版SWAP模型
结合前面的学习，实现一个简化版的SWAP模型，包含：

1. **土壤水力特性模块**（基于van Genuchten模型）
2. **插值计算模块**（基于样条插值）
3. **Richards方程求解器**（有限差分方法）
4. **简单的边界条件处理**
5. **结果可视化和分析**

这个项目将帮助您：
- 整合所有学到的概念
- 理解模块间的协作方式
- 体验完整的建模流程
- 为参与PySWAP开发做准备

---

*本学习计划基于SWAP 4.2.0架构分析报告制定，包含了详细的实践指导和代码示例。建议按照自己的背景和目标选择合适的学习路径，并通过实际编程练习来巩固理论知识。*
