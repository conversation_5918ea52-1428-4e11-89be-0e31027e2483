"""
PySWAP核心计算模块

包含使用Cython实现的高性能计算模块：
- functions: 核心数学函数
- interpolation: 插值计算
- soilwater: 土壤水分计算
- swap_interface: 主模型接口
"""

# 尝试导入编译的模块
try:
    from . import functions
    FUNCTIONS_AVAILABLE = True
except ImportError:
    FUNCTIONS_AVAILABLE = False

try:
    from . import interpolation
    INTERPOLATION_AVAILABLE = True
except ImportError:
    INTERPOLATION_AVAILABLE = False

try:
    from . import soilwater
    SOILWATER_AVAILABLE = True
except ImportError:
    SOILWATER_AVAILABLE = False

try:
    from . import swap_interface
    SWAP_INTERFACE_AVAILABLE = True
except ImportError:
    SWAP_INTERFACE_AVAILABLE = False

# 导入异常类
from .exceptions import (
    SwapError, ConvergenceError, BoundaryError,
    InitializationError, ConfigurationError,
    MemoryError, TimeStepError, MassBalanceError
)

__all__ = [
    # 模块可用性标志
    'FUNCTIONS_AVAILABLE',
    'INTERPOLATION_AVAILABLE', 
    'SOILWATER_AVAILABLE',
    'SWAP_INTERFACE_AVAILABLE',
    
    # 异常类
    'SwapError',
    'ConvergenceError',
    'BoundaryError',
    'InitializationError',
    'ConfigurationError',
    'MemoryError',
    'TimeStepError',
    'MassBalanceError',
]

def get_module_status():
    """获取核心模块的编译状态"""
    return {
        'functions': FUNCTIONS_AVAILABLE,
        'interpolation': INTERPOLATION_AVAILABLE,
        'soilwater': SOILWATER_AVAILABLE,
        'swap_interface': SWAP_INTERFACE_AVAILABLE,
    }
