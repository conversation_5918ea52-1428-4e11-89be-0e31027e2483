# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
# cython: initializedcheck=False

"""
土壤水分计算模块

封装soilwater.f90中的土壤水分状态计算功能，包括：
- Richards方程求解
- 水分通量计算
- 水量存储计算
- 质量平衡检查

保持与原始Fortran代码的完全数值兼容性。
"""

import numpy as np
cimport numpy as cnp
cimport cython
from libc.math cimport fabs, sqrt
from ..core.exceptions import SwapError, ConvergenceError, MassBalanceError

# 导入C接口声明
cdef extern from "../c_interface/swap_c_interface.h":
    ctypedef double real8_t
    ctypedef int integer_t
    
    # 土壤水分计算的C接口声明
    void c_soilwater(integer_t task)
    void c_watstor()
    void c_fluxes()


@cython.boundscheck(False)
@cython.wraparound(False)
def soilwater_task(int task):
    """
    执行土壤水分计算任务
    
    对应Fortran子程序: soilwater(task)
    
    Parameters:
    -----------
    task : int
        任务类型
        1 = 初始化土壤水分状态变量
        2 = 计算土壤水分状态变量
        3 = 更新状态变量
        
    Raises:
    -------
    ValueError
        当任务类型无效时
    SwapError
        当计算失败时
    """
    if task not in [1, 2, 3]:
        raise ValueError(f"无效的任务类型: {task}，必须是1、2或3")
    
    try:
        c_soilwater(<integer_t>task)
    except Exception as e:
        raise SwapError(f"土壤水分计算失败: {e}", module="soilwater")


@cython.boundscheck(False)
@cython.wraparound(False)
def calculate_water_storage():
    """
    计算土壤剖面的水量存储
    
    对应Fortran子程序: watstor()
    
    Returns:
    --------
    float
        总水量存储 (cm)
        
    Raises:
    -------
    SwapError
        当计算失败时
    """
    try:
        c_watstor()
        # TODO: 从全局状态获取计算结果
        return 0.0  # 占位符返回值
    except Exception as e:
        raise SwapError(f"水量存储计算失败: {e}", module="watstor")


@cython.boundscheck(False)
@cython.wraparound(False)
def calculate_fluxes():
    """
    计算土壤层间的水分通量
    
    对应Fortran子程序: fluxes()
    
    Returns:
    --------
    ndarray[float]
        各层间的水分通量 (cm/d)
        
    Raises:
    -------
    SwapError
        当计算失败时
    """
    try:
        c_fluxes()
        # TODO: 从全局状态获取通量数组
        return np.zeros(5001)  # 占位符返回值
    except Exception as e:
        raise SwapError(f"通量计算失败: {e}", module="fluxes")


class SoilWaterSolver:
    """
    土壤水分求解器类
    
    提供面向对象的土壤水分计算接口，
    封装Richards方程的数值求解过程。
    """
    
    def __init__(self, max_nodes=5000):
        """
        初始化土壤水分求解器
        
        Parameters:
        -----------
        max_nodes : int
            最大节点数
        """
        self.max_nodes = max_nodes
        self.initialized = False
        
        # 状态数组
        self.h = np.zeros(max_nodes)          # 压力水头
        self.theta = np.zeros(max_nodes)      # 含水量
        self.k = np.zeros(max_nodes)          # 水力传导度
        self.q = np.zeros(max_nodes + 1)      # 通量
        self.dz = np.zeros(max_nodes)         # 层厚
        
        # 求解参数
        self.dt = 0.01                        # 时间步长
        self.tolerance = 1e-6                 # 收敛容差
        self.max_iterations = 50              # 最大迭代次数
    
    def initialize_profile(self, depths, initial_heads):
        """
        初始化土壤剖面
        
        Parameters:
        -----------
        depths : array_like
            节点深度 (cm)
        initial_heads : array_like
            初始压力水头 (cm)
        """
        depths = np.asarray(depths)
        initial_heads = np.asarray(initial_heads)
        
        if len(depths) != len(initial_heads):
            raise ValueError("深度和水头数组长度必须相同")
        
        if len(depths) > self.max_nodes:
            raise ValueError(f"节点数 {len(depths)} 超过最大值 {self.max_nodes}")
        
        self.numnod = len(depths)
        
        # 计算层厚
        self.dz[0] = depths[0] if len(depths) > 0 else 0
        for i in range(1, self.numnod):
            self.dz[i] = depths[i] - depths[i-1]
        
        # 设置初始水头
        self.h[:self.numnod] = initial_heads
        
        # 计算初始含水量和传导度
        self._update_hydraulic_properties()
        
        self.initialized = True
    
    def _update_hydraulic_properties(self):
        """更新水力特性"""
        if not self.initialized:
            return
        
        # TODO: 调用核心函数计算含水量和传导度
        # 这里使用简化实现
        for i in range(self.numnod):
            # 简单的van Genuchten关系
            if self.h[i] >= 0:
                self.theta[i] = 0.43  # 饱和含水量
                self.k[i] = 10.0      # 饱和传导度
            else:
                # 简化的非饱和关系
                se = 1.0 / (1.0 + abs(0.02 * self.h[i]) ** 1.31) ** (1.0 - 1.0/1.31)
                self.theta[i] = 0.01 + (0.43 - 0.01) * se
                self.k[i] = 10.0 * se ** 0.5 * (1.0 - (1.0 - se ** (1.31/(1.31-1))) ** (1.0 - 1.0/1.31)) ** 2
    
    def solve_time_step(self, dt=None):
        """
        求解一个时间步长
        
        Parameters:
        -----------
        dt : float, optional
            时间步长，如果未提供则使用默认值
            
        Returns:
        --------
        dict
            求解结果信息
        """
        if not self.initialized:
            raise SwapError("求解器未初始化")
        
        if dt is not None:
            self.dt = dt
        
        # 保存旧状态
        h_old = self.h.copy()
        
        # 执行求解 (简化实现)
        try:
            soilwater_task(2)  # 调用Fortran计算
            
            # 检查收敛性
            max_change = np.max(np.abs(self.h - h_old))
            converged = max_change < self.tolerance
            
            if not converged:
                raise ConvergenceError(f"时间步求解未收敛，最大变化: {max_change}")
            
            # 更新水力特性
            self._update_hydraulic_properties()
            
            return {
                'converged': True,
                'max_change': max_change,
                'iterations': 1,  # 简化实现
                'time_step': self.dt,
            }
            
        except Exception as e:
            # 恢复旧状态
            self.h = h_old
            raise SwapError(f"时间步求解失败: {e}")
    
    def check_mass_balance(self):
        """
        检查质量平衡
        
        Returns:
        --------
        dict
            质量平衡信息
        """
        if not self.initialized:
            raise SwapError("求解器未初始化")
        
        # 计算当前水量
        current_water = np.sum(self.theta[:self.numnod] * self.dz[:self.numnod])
        
        # TODO: 实现完整的质量平衡检查
        balance_error = 0.0  # 占位符
        
        return {
            'total_water': current_water,
            'balance_error': balance_error,
            'relative_error': balance_error / current_water if current_water > 0 else 0,
        }
    
    def get_profile_data(self):
        """
        获取土壤剖面数据
        
        Returns:
        --------
        dict
            剖面数据字典
        """
        if not self.initialized:
            raise SwapError("求解器未初始化")
        
        return {
            'depths': np.cumsum(self.dz[:self.numnod]),
            'pressure_heads': self.h[:self.numnod].copy(),
            'water_contents': self.theta[:self.numnod].copy(),
            'hydraulic_conductivities': self.k[:self.numnod].copy(),
            'fluxes': self.q[:self.numnod+1].copy(),
            'layer_thickness': self.dz[:self.numnod].copy(),
        }
