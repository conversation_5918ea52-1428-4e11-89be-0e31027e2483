"""
PySWAP性能基准测试

测试各个模块的性能表现，与原始Fortran代码进行对比
"""

import pytest
import numpy as np
import time
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    import pyswap
    from pyswap.models.state_manager import SwapState
    from pyswap.models.swap_model import SwapModel
    PYSWAP_AVAILABLE = True
except ImportError:
    PYSWAP_AVAILABLE = False

try:
    from pyswap.core import functions
    FUNCTIONS_AVAILABLE = True
except ImportError:
    FUNCTIONS_AVAILABLE = False


@pytest.mark.skipif(not PYSWAP_AVAILABLE, reason="PySWAP not available")
class TestStateManagerPerformance:
    """状态管理器性能测试"""
    
    def test_state_initialization_time(self):
        """测试状态初始化时间"""
        sizes = [10, 50, 100, 500, 1000]
        times = []
        
        for size in sizes:
            start_time = time.perf_counter()
            
            state = SwapState(max_nodes=size)
            config = {'max_nodes': size, 'swkmean': 6}
            state.initialize(config)
            
            end_time = time.perf_counter()
            elapsed = end_time - start_time
            times.append(elapsed)
            
            print(f"节点数 {size:4d}: 初始化时间 {elapsed*1000:.2f} ms")
        
        # 检查时间复杂度是否合理 (应该接近线性)
        assert all(t < 0.1 for t in times), "初始化时间过长"
    
    def test_state_validation_time(self):
        """测试状态验证时间"""
        state = SwapState(max_nodes=1000)
        config = {'max_nodes': 1000, 'swkmean': 6}
        state.initialize(config)
        
        # 多次验证测试
        times = []
        for _ in range(10):
            start_time = time.perf_counter()
            issues = state.validate_state()
            end_time = time.perf_counter()
            times.append(end_time - start_time)
        
        avg_time = np.mean(times)
        print(f"状态验证平均时间: {avg_time*1000:.2f} ms")
        
        assert avg_time < 0.01, "状态验证时间过长"


@pytest.mark.skipif(not FUNCTIONS_AVAILABLE, reason="Core functions not available")
class TestCoreFunctionPerformance:
    """核心函数性能测试"""
    
    def test_hcomean_performance(self):
        """测试hcomean函数性能"""
        n_calls = 10000
        
        # 准备测试数据
        methods = np.random.randint(1, 7, n_calls)
        kup_values = np.random.uniform(0.1, 100.0, n_calls)
        klow_values = np.random.uniform(0.1, 100.0, n_calls)
        dzup_values = np.random.uniform(0.1, 10.0, n_calls)
        dzlow_values = np.random.uniform(0.1, 10.0, n_calls)
        
        # 单次调用测试
        start_time = time.perf_counter()
        for i in range(n_calls):
            result = pyswap.functions.hcomean(
                int(methods[i]), kup_values[i], klow_values[i],
                dzup_values[i], dzlow_values[i]
            )
        end_time = time.perf_counter()
        
        total_time = end_time - start_time
        time_per_call = total_time / n_calls * 1e6  # microseconds
        
        print(f"hcomean性能: {n_calls} 次调用，总时间 {total_time:.3f}s")
        print(f"平均每次调用: {time_per_call:.2f} μs")
        
        # 性能要求：每次调用应在10微秒以内
        assert time_per_call < 10.0, f"hcomean性能不达标: {time_per_call:.2f} μs/call"
    
    def test_batch_vs_single_calls(self):
        """比较批量调用与单次调用的性能"""
        n = 1000
        
        nodes = np.arange(1, n+1, dtype=np.int32)
        heads = np.random.uniform(-1000, 0, n)
        
        # 单次调用
        start_time = time.perf_counter()
        results_single = []
        for i in range(n):
            try:
                result = pyswap.functions.watcon(int(nodes[i]), heads[i])
                results_single.append(result)
            except:
                results_single.append(0.0)  # 处理错误
        end_time = time.perf_counter()
        time_single = end_time - start_time
        
        # 批量调用
        start_time = time.perf_counter()
        try:
            results_batch = pyswap.functions.batch_watcon(nodes, heads)
        except:
            results_batch = np.zeros(n)  # 处理错误
        end_time = time.perf_counter()
        time_batch = end_time - start_time
        
        print(f"单次调用时间: {time_single:.3f}s")
        print(f"批量调用时间: {time_batch:.3f}s")
        print(f"性能提升: {time_single/time_batch:.1f}x")
        
        # 批量调用应该更快
        if time_batch > 0:
            assert time_batch < time_single, "批量调用应该更快"


@pytest.mark.skipif(not PYSWAP_AVAILABLE, reason="PySWAP not available")
class TestModelPerformance:
    """模型整体性能测试"""
    
    def test_model_creation_time(self):
        """测试模型创建时间"""
        sizes = [10, 50, 100, 500]
        
        for size in sizes:
            start_time = time.perf_counter()
            model = SwapModel(max_nodes=size)
            end_time = time.perf_counter()
            
            elapsed = end_time - start_time
            print(f"模型创建 ({size} 节点): {elapsed*1000:.2f} ms")
            
            assert elapsed < 0.1, f"模型创建时间过长: {elapsed:.3f}s"
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 基线内存
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大型模型
        models = []
        for i in range(5):
            model = SwapModel(max_nodes=1000)
            models.append(model)
        
        # 测量内存增长
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = current_memory - baseline_memory
        
        print(f"基线内存: {baseline_memory:.1f} MB")
        print(f"当前内存: {current_memory:.1f} MB")
        print(f"内存增长: {memory_increase:.1f} MB")
        print(f"每个模型: {memory_increase/5:.1f} MB")
        
        # 内存使用应该合理 (每个1000节点模型不超过50MB)
        assert memory_increase < 250, f"内存使用过多: {memory_increase:.1f} MB"


def benchmark_summary():
    """性能基准测试摘要"""
    print("\n" + "="*60)
    print("PySWAP性能基准测试摘要")
    print("="*60)
    
    # 检查模块可用性
    print(f"PySWAP可用: {PYSWAP_AVAILABLE}")
    print(f"核心函数可用: {FUNCTIONS_AVAILABLE}")
    
    if PYSWAP_AVAILABLE:
        try:
            version_info = pyswap.get_version_info()
            print(f"版本: {version_info.get('version', 'unknown')}")
            print(f"编译模块: {version_info.get('compiled_modules', False)}")
        except:
            print("无法获取版本信息")
    
    print("\n性能目标:")
    print("- 状态初始化: < 100ms (1000节点)")
    print("- 核心函数调用: < 10μs/call")
    print("- 批量操作: > 2x性能提升")
    print("- 内存使用: < 50MB/1000节点")


if __name__ == "__main__":
    benchmark_summary()
    pytest.main([__file__, "-v", "--tb=short"])
