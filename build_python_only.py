#!/usr/bin/env python3
"""
仅Python构建脚本

在没有Fortran编译器的情况下，先构建Python部分，
为后续的完整编译做准备。
"""

import os
import sys
from pathlib import Path

def create_init_files():
    """创建所有必要的__init__.py文件"""
    print("📁 创建__init__.py文件...")
    
    init_files = [
        "tests/__init__.py",
        "tests/unit/__init__.py",
        "tests/integration/__init__.py", 
        "tests/performance/__init__.py",
    ]
    
    for init_file in init_files:
        init_path = Path(init_file)
        if not init_path.exists():
            init_path.parent.mkdir(parents=True, exist_ok=True)
            init_path.write_text('"""Package initialization"""')
            print(f"✅ 创建: {init_file}")

def create_placeholder_c_files():
    """创建C文件占位符"""
    print("📁 创建C文件占位符...")
    
    c_files = {
        "c_interface/c_soilwater.c": '''/*
 * 土壤水分计算C接口 - 占位符实现
 */

#include "swap_c_interface.h"

void c_soilwater(integer_t task) {
    /* TODO: 实现土壤水分计算 */
}

void c_watstor(void) {
    /* TODO: 实现水量存储计算 */
}

void c_fluxes(void) {
    /* TODO: 实现通量计算 */
}
''',
        "c_interface/c_swap_interface.c": '''/*
 * 主SWAP接口C实现 - 占位符
 */

#include "swap_c_interface.h"

void c_swap(integer_t icaller, integer_t itask, 
            swap_input_t* toswap, swap_output_t* fromswap) {
    /* TODO: 实现主SWAP接口 */
    if (fromswap != NULL) {
        fromswap->ierrorcode = 0;
        fromswap->numnodes = 10;
        fromswap->tpot = 0.0;
        fromswap->tact = 0.0;
    }
}

integer_t c_initialize_swap_state(void) {
    return 0;
}

void c_cleanup_swap_state(void) {
    /* TODO: 清理状态 */
}

integer_t c_get_state_variable(const char* var_name, void* value, integer_t var_type) {
    return 0;
}

integer_t c_set_state_variable(const char* var_name, const void* value, integer_t var_type) {
    return 0;
}

integer_t c_allocate_work_arrays(integer_t numnod) {
    return 0;
}

void c_deallocate_work_arrays(void) {
    /* TODO: 释放数组 */
}

integer_t c_check_memory_usage(void) {
    return 0;
}
''',
    }
    
    for file_path, content in c_files.items():
        c_path = Path(file_path)
        if not c_path.exists():
            c_path.parent.mkdir(parents=True, exist_ok=True)
            c_path.write_text(content)
            print(f"✅ 创建: {file_path}")

def test_python_imports():
    """测试Python模块导入"""
    print("\n🧪 测试Python模块导入...")
    
    try:
        # 测试主包导入
        import pyswap
        print(f"✅ 主包导入成功: {pyswap.__version__}")
        
        # 测试状态管理器
        from pyswap.models.state_manager import SwapState
        state = SwapState()
        print("✅ 状态管理器导入成功")
        
        # 测试模型类
        from pyswap.models.swap_model import SwapModel
        model = SwapModel()
        print("✅ 模型类导入成功")
        
        # 测试异常类
        from pyswap.core.exceptions import SwapError
        print("✅ 异常类导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Python模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        # 测试状态管理器初始化
        from pyswap.models.state_manager import SwapState
        state = SwapState(max_nodes=10)
        
        config = {'max_nodes': 10, 'swkmean': 6}
        state.initialize(config)
        
        summary = state.get_summary()
        print(f"状态摘要: {summary}")
        
        # 测试状态验证
        issues = state.validate_state()
        print(f"验证问题: {len(issues)}个")
        
        # 测试模型创建
        from pyswap.models.swap_model import SwapModel
        model = SwapModel(max_nodes=5)
        
        model_summary = model.get_state_summary()
        print(f"模型状态: {model_summary}")
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 PySWAP仅Python构建脚本")
    print("=" * 50)
    
    # 创建必要文件
    create_init_files()
    create_placeholder_c_files()
    
    # 测试Python部分
    if not test_python_imports():
        print("\n❌ Python模块导入失败")
        return False
    
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败")
        return False
    
    print("\n🎉 Python部分构建成功!")
    print("\n📖 下一步:")
    print("  1. 安装Fortran编译器 (gfortran)")
    print("  2. 运行 'python build_extensions.py' 编译Cython扩展")
    print("  3. 运行 'python examples/basic_usage.py' 测试完整功能")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
