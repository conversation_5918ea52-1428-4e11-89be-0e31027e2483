"""
SWAP模型状态管理器

将variables.f90中的全局变量转换为Python类结构，
提供面向对象的状态管理和数据访问接口。
"""

import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from ..core.exceptions import SwapError, InitializationError


@dataclass
class TimeControlState:
    """时间控制状态变量"""
    # 基本时间变量
    t: float = 0.0                    # 自日历年开始的时间
    t1900: float = 0.0                # 自1900年开始的时间
    tcum: float = 0.0                 # 自模拟开始的时间
    dt: float = 0.01                  # 时间步长
    dtmax: float = 1.0                # 最大时间步长
    dtmin: float = 1e-6               # 最小时间步长
    
    # 日期和计数器
    daycum: int = 0                   # 自模拟开始的天数
    daynr: int = 0                    # 日历年中的天数
    imonth: int = 0                   # 月份
    iyear: int = 0                    # 年份
    isteps: int = 0                   # 自当天开始的时间步数
    
    # 控制标志
    fldayend: bool = False            # 一天结束标志
    fldaystart: bool = False          # 一天开始标志
    fldecdt: bool = False             # 减少时间步长标志
    flrunend: bool = False            # 运行结束标志
    
    # 输出控制
    period: int = 1                   # 输出间隔长度
    outper: float = 1.0               # 实际输出间隔长度
    floutput: bool = False            # 输出时间标志


@dataclass
class MeteoState:
    """气象状态变量 - 对应variables.f90中的meteo variables"""
    # 当前气象数据
    tmn: float = 0.0                  # 当日最低温度 (°C)
    tmx: float = 0.0                  # 当日最高温度 (°C)
    tav: float = 0.0                  # 当日平均温度 (°C)
    tavd: float = 0.0                 # 白天平均温度 (°C)
    tmnr: float = 0.0                 # 过去7天最低温度平均 (°C)
    rh: float = 0.0                   # 相对湿度 (-)
    wind: float = 0.0                 # 风速 (m/s)
    rad: float = 0.0                  # 全球太阳辐射 (J/m²/d)
    grai: float = 0.0                 # 日总降雨量 (mm/d)
    nraida: float = 0.0               # 日平均净降雨量 (mm/d)
    peva: float = 0.0                 # 潜在土壤蒸发 (mm/d)
    ptra: float = 0.0                 # 潜在蒸腾 (mm/d)
    tra: float = 0.0                  # 实际蒸腾 (mm/d)
    atmdem: float = 0.0               # 大气需求 (mm/d)

    # 气象站参数
    alt: float = 0.0                  # 气象站海拔 (m)
    altw: float = 2.0                 # 风速测量高度 (m)
    lat: float = 52.0                 # 纬度 (度)
    angstroma: float = 0.25           # Angstrom系数a
    angstromb: float = 0.50           # Angstrom系数b

    # 年度气象数据数组 (366天)
    aetr: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日ETref数据
    ahum: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日湿度数据
    arad: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日辐射数据
    atmn: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日最低温度数据
    atmx: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日最高温度数据
    arai: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日降雨数据
    awin: np.ndarray = field(default_factory=lambda: np.zeros(366))    # 日风速数据
    wet: np.ndarray = field(default_factory=lambda: np.zeros(366))     # 作物湿润时间

    # 详细气象数据 (用于亚日尺度计算)
    atmin7: np.ndarray = field(default_factory=lambda: np.zeros(7))     # 过去7天最低温度
    atav: np.ndarray = field(default_factory=lambda: np.zeros(96))      # 详细温度记录
    epot: np.ndarray = field(default_factory=lambda: np.zeros(96))      # 详细Epot记录
    tpot: np.ndarray = field(default_factory=lambda: np.zeros(96))      # 详细Tpot记录
    grain: np.ndarray = field(default_factory=lambda: np.zeros(96))     # 详细降雨记录
    nrain: np.ndarray = field(default_factory=lambda: np.zeros(96))     # 详细净降雨记录

    # 控制变量
    daymeteo: int = 1                 # 气象数据日历日
    yearmeteo: int = 2000             # 气象数据年份
    daynrfirst: int = 1               # 当年第一个气象数据日
    daynrlast: int = 365              # 当年最后一个气象数据日
    swetr: int = 0                    # ETref使用开关
    swrain: int = 0                   # 降雨数据开关
    swinter: int = 0                  # 截留方法开关
    swdivide: int = 0                 # ET分割开关
    swetsine: int = 0                 # 正弦分布开关
    swmetdetail: int = 0              # 详细气象数据开关

    # 累积变量
    cgrai: float = 0.0                # 累积总降雨量
    cnrai: float = 0.0                # 累积净降雨量
    cevap: float = 0.0                # 累积实际土壤蒸发
    cpeva: float = 0.0                # 累积潜在土壤蒸发
    cptra: float = 0.0                # 累积潜在蒸腾
    caintc: float = 0.0               # 累积截留量

    # 中间变量
    ievap: float = 0.0                # 中间实际土壤蒸发
    inrai: float = 0.0                # 中间净降雨量
    ipeva: float = 0.0                # 中间潜在土壤蒸发
    iptra: float = 0.0                # 中间潜在蒸腾

    # 文件路径
    metfil: str = ""                  # 气象输入文件名
    rainfil: str = ""                 # 详细降雨文件名
    pathatm: str = ""                 # 气象文件路径


@dataclass
class SoilWaterState:
    """土壤水分状态变量"""
    # 基本状态数组 (最大5000层)
    h: np.ndarray = field(default_factory=lambda: np.zeros(5000))      # 压力水头 (cm)
    theta: np.ndarray = field(default_factory=lambda: np.zeros(5000))  # 体积含水量 (-)
    k: np.ndarray = field(default_factory=lambda: np.zeros(5000))      # 水力传导度 (cm/d)
    q: np.ndarray = field(default_factory=lambda: np.zeros(5001))      # 水分通量 (cm/d)
    
    # 网格参数
    numnod: int = 0                   # 实际节点数
    dz: np.ndarray = field(default_factory=lambda: np.zeros(5000))     # 土层厚度 (cm)
    z: np.ndarray = field(default_factory=lambda: np.zeros(5000))      # 节点深度 (cm)
    
    # 水力特性参数
    kmean: np.ndarray = field(default_factory=lambda: np.zeros(5001))  # 平均传导度
    dimoca: np.ndarray = field(default_factory=lambda: np.zeros(5000)) # 微分含水量
    
    # 边界条件
    qtop: float = 0.0                 # 顶部通量 (cm/d)
    qbot: float = 0.0                 # 底部通量 (cm/d)
    pond: float = 0.0                 # 地表积水 (cm)
    
    # 水量平衡
    volact: float = 0.0               # 当前水量 (cm)
    volini: float = 0.0               # 初始水量 (cm)


@dataclass
class CropState:
    """作物状态变量"""
    # 作物基本参数
    ch: float = 0.0                   # 作物高度 (cm)
    zroot: float = 0.0                # 根深 (cm)
    lai: float = 0.0                  # 叶面积指数 (m²/m²)
    icrop: int = 0                    # 作物存在标志
    
    # 蒸腾相关
    tpot: float = 0.0                 # 潜在蒸腾 (cm/d)
    tact: float = 0.0                 # 实际蒸腾 (cm/d)
    
    # 根系吸水
    rwu: np.ndarray = field(default_factory=lambda: np.zeros(5000))    # 根系吸水 (cm/d)


class SwapState:
    """
    SWAP模型完整状态管理器
    
    将variables.f90中的全局变量组织为面向对象的结构，
    提供类型安全的状态访问和修改接口。
    """
    
    def __init__(self, max_nodes: int = 5000):
        """
        初始化SWAP状态管理器
        
        Parameters:
        -----------
        max_nodes : int
            最大节点数，默认5000 (对应MACP)
        """
        self.max_nodes = max_nodes
        self._initialized = False
        
        # 初始化各个状态组件
        self.time = TimeControlState()
        self.meteo = MeteoState()
        self.soilwater = SoilWaterState()
        self.crop = CropState()
        
        # 控制参数
        self.swkmean = 6                  # 默认使用加权调和平均
        self.swsophy = 0                  # 土壤物理选项
        self.swinco = 1                   # 初始条件选项
        
        # 错误状态
        self.ierrorcode = 0
        self.error_message = ""
    
    def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        初始化状态管理器
        
        Parameters:
        -----------
        config : dict, optional
            配置参数字典
        """
        if config is None:
            config = {}
        
        try:
            # 设置基本参数
            self.max_nodes = config.get('max_nodes', 5000)
            self.swkmean = config.get('swkmean', 6)
            self.swsophy = config.get('swsophy', 0)
            
            # 初始化数组大小
            if self.max_nodes > 5000:
                raise ValueError("节点数不能超过5000")
            
            # 重新分配数组
            self._reallocate_arrays()
            
            self._initialized = True
            self.ierrorcode = 0
            
        except Exception as e:
            self.ierrorcode = 1
            self.error_message = str(e)
            raise InitializationError(f"状态管理器初始化失败: {e}")
    
    def _reallocate_arrays(self) -> None:
        """重新分配状态数组"""
        # 土壤水分状态数组
        self.soilwater.h = np.zeros(self.max_nodes)
        self.soilwater.theta = np.zeros(self.max_nodes)
        self.soilwater.k = np.zeros(self.max_nodes)
        self.soilwater.q = np.zeros(self.max_nodes + 1)
        self.soilwater.dz = np.zeros(self.max_nodes)
        self.soilwater.z = np.zeros(self.max_nodes)
        self.soilwater.kmean = np.zeros(self.max_nodes + 1)
        self.soilwater.dimoca = np.zeros(self.max_nodes)
        
        # 作物状态数组
        self.crop.rwu = np.zeros(self.max_nodes)
    
    def validate_state(self) -> List[str]:
        """
        验证当前状态的一致性
        
        Returns:
        --------
        List[str]
            发现的问题列表
        """
        issues = []
        
        if not self._initialized:
            issues.append("状态管理器未初始化")
            return issues
        
        # 检查数组大小一致性
        if len(self.soilwater.h) != self.max_nodes:
            issues.append("压力水头数组大小不匹配")
        
        if len(self.soilwater.theta) != self.max_nodes:
            issues.append("含水量数组大小不匹配")
        
        # 检查物理约束
        if np.any(self.soilwater.theta < 0) or np.any(self.soilwater.theta > 1):
            issues.append("含水量超出物理范围 [0,1]")
        
        if np.any(self.soilwater.k < 0):
            issues.append("水力传导度为负值")
        
        if self.soilwater.numnod > self.max_nodes:
            issues.append("实际节点数超过最大节点数")
        
        return issues
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取状态摘要信息
        
        Returns:
        --------
        Dict[str, Any]
            状态摘要字典
        """
        if not self._initialized:
            return {"status": "未初始化"}
        
        return {
            "status": "已初始化",
            "max_nodes": self.max_nodes,
            "actual_nodes": self.soilwater.numnod,
            "current_time": self.time.t,
            "time_step": self.time.dt,
            "total_water": self.soilwater.volact,
            "error_code": self.ierrorcode,
            "validation_issues": len(self.validate_state()),
        }
    
    def reset(self) -> None:
        """重置状态到初始值"""
        if self._initialized:
            self._reallocate_arrays()
            self.time = TimeControlState()
            self.meteo = MeteoState()
            self.crop = CropState()
            self.ierrorcode = 0
            self.error_message = ""
