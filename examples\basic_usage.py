#!/usr/bin/env python3
"""
PySWAP基本使用示例

演示如何使用PySWAP进行基本的土壤水分模拟
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import pyswap
    print(f"✅ 成功导入PySWAP版本 {pyswap.__version__}")
except ImportError as e:
    print(f"❌ 导入PySWAP失败: {e}")
    print("请确保已编译Cython扩展模块")
    sys.exit(1)

def test_state_manager():
    """测试状态管理器"""
    print("\n🧪 测试状态管理器...")
    
    try:
        # 创建状态管理器
        state = pyswap.SwapState(max_nodes=100)
        
        # 初始化
        config = {
            'max_nodes': 100,
            'swkmean': 6,
            'swsophy': 0,
        }
        state.initialize(config)
        
        # 获取状态摘要
        summary = state.get_summary()
        print(f"状态摘要: {summary}")
        
        # 验证状态
        issues = state.validate_state()
        if issues:
            print(f"⚠️  发现问题: {issues}")
        else:
            print("✅ 状态验证通过")
            
        return True
        
    except Exception as e:
        print(f"❌ 状态管理器测试失败: {e}")
        return False

def test_model_interface():
    """测试模型接口"""
    print("\n🧪 测试模型接口...")
    
    try:
        # 创建模型实例
        model = pyswap.SwapModel(max_nodes=50)
        
        # 获取状态摘要
        summary = model.get_state_summary()
        print(f"模型状态: {summary}")
        
        # 测试上下文管理器
        with pyswap.SwapModel() as test_model:
            print("✅ 上下文管理器工作正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型接口测试失败: {e}")
        return False

def test_core_functions():
    """测试核心函数（如果可用）"""
    print("\n🧪 测试核心函数...")
    
    if not pyswap._COMPILED_MODULES_AVAILABLE:
        print("⚠️  编译模块不可用，跳过核心函数测试")
        return True
    
    try:
        # 测试hcomean函数
        result = pyswap.functions.hcomean(1, 10.0, 20.0, 5.0, 5.0)
        expected = 15.0
        if abs(result - expected) < 1e-10:
            print(f"✅ hcomean测试通过: {result}")
        else:
            print(f"❌ hcomean测试失败: 期望{expected}, 得到{result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 核心函数测试失败: {e}")
        return False

def demonstrate_basic_workflow():
    """演示基本工作流程"""
    print("\n🎯 演示基本工作流程...")
    
    try:
        # 1. 创建模型
        print("1. 创建SWAP模型实例...")
        model = pyswap.SwapModel(max_nodes=10)
        
        # 2. 配置模型
        print("2. 配置模型参数...")
        config = {
            'max_nodes': 10,
            'swkmean': 6,  # 加权调和平均
            'dt': 0.01,    # 时间步长
            'dtmax': 1.0,  # 最大时间步长
        }
        
        # 3. 初始化（目前只是接口测试）
        print("3. 初始化模型...")
        try:
            model.initialize(config_dict=config)
            print("✅ 模型初始化成功")
        except Exception as e:
            print(f"⚠️  模型初始化失败（预期的）: {e}")
        
        # 4. 显示状态
        print("4. 检查模型状态...")
        summary = model.get_state_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流程演示失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PySWAP基本使用示例")
    print("=" * 50)
    
    # 显示版本信息
    try:
        version_info = pyswap.get_version_info()
        print("📋 版本信息:")
        for key, value in version_info.items():
            print(f"   {key}: {value}")
    except Exception as e:
        print(f"⚠️  无法获取版本信息: {e}")
    
    # 检查环境
    try:
        issues = pyswap.check_environment()
        if issues:
            print(f"\n⚠️  环境问题: {issues}")
        else:
            print("\n✅ 环境检查通过")
    except Exception as e:
        print(f"⚠️  环境检查失败: {e}")
    
    # 运行测试
    tests = [
        test_state_manager,
        test_model_interface,
        test_core_functions,
        demonstrate_basic_workflow,
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，这在开发阶段是正常的")

if __name__ == "__main__":
    main()
