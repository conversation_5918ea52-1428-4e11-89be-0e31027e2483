#!/usr/bin/env python3
"""
构建脚本 - 编译PySWAP Cython扩展模块

这个脚本用于编译和测试Cython扩展模块，
提供详细的编译信息和错误诊断。
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """检查编译依赖"""
    print("🔍 检查编译依赖...")
    
    issues = []
    
    # 检查Python包
    try:
        import numpy
        print(f"✅ NumPy {numpy.__version__}")
    except ImportError:
        issues.append("NumPy未安装")
    
    try:
        import Cython
        print(f"✅ Cython {Cython.__version__}")
    except ImportError:
        issues.append("Cython未安装")
    
    try:
        import setuptools
        print(f"✅ setuptools {setuptools.__version__}")
    except ImportError:
        issues.append("setuptools未安装")
    
    # 检查编译器
    try:
        result = subprocess.run(['gcc', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ GCC: {version_line}")
        else:
            issues.append("GCC不可用")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        issues.append("GCC编译器未找到")
    
    # 检查Fortran编译器
    try:
        result = subprocess.run(['gfortran', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ gfortran: {version_line}")
        else:
            issues.append("gfortran不可用")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        issues.append("gfortran编译器未找到")
    
    return issues

def create_missing_files():
    """创建缺失的必要文件"""
    print("📁 创建缺失的文件...")
    
    # 创建__init__.py文件
    init_files = [
        "pyswap/core/__init__.py",
        "pyswap/models/__init__.py", 
        "pyswap/utils/__init__.py",
        "tests/__init__.py",
        "tests/unit/__init__.py",
        "tests/integration/__init__.py",
        "tests/performance/__init__.py",
    ]
    
    for init_file in init_files:
        init_path = Path(init_file)
        if not init_path.exists():
            init_path.parent.mkdir(parents=True, exist_ok=True)
            init_path.write_text('"""Package initialization"""')
            print(f"✅ 创建: {init_file}")
    
    # 创建缺失的C文件占位符
    c_files = [
        "c_interface/c_soilwater.c",
        "c_interface/c_swap_interface.c",
    ]
    
    for c_file in c_files:
        c_path = Path(c_file)
        if not c_path.exists():
            c_path.parent.mkdir(parents=True, exist_ok=True)
            c_path.write_text(f'''/*
 * {c_file} - 占位符实现
 * TODO: 实现实际的C接口函数
 */

#include "swap_c_interface.h"

/* 占位符函数 - 需要实际实现 */
''')
            print(f"✅ 创建占位符: {c_file}")

def build_extensions():
    """编译Cython扩展"""
    print("🔨 开始编译Cython扩展...")
    
    start_time = time.time()
    
    try:
        # 运行setup.py build_ext
        cmd = [sys.executable, "setup.py", "build_ext", "--inplace", "--verbose"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            elapsed = time.time() - start_time
            print(f"✅ 编译成功! 耗时: {elapsed:.1f}秒")
            print("编译输出:")
            print(result.stdout)
            return True
        else:
            print("❌ 编译失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 编译超时!")
        return False
    except Exception as e:
        print(f"❌ 编译过程出错: {e}")
        return False

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    test_modules = [
        "pyswap",
        "pyswap.core.functions",
        "pyswap.core.interpolation", 
        "pyswap.models.state_manager",
        "pyswap.models.swap_model",
    ]
    
    success_count = 0
    
    for module in test_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module}: {e}")
        except Exception as e:
            print(f"⚠️  {module}: {e}")
    
    print(f"导入测试: {success_count}/{len(test_modules)} 成功")
    return success_count == len(test_modules)

def run_basic_tests():
    """运行基本功能测试"""
    print("🧪 运行基本功能测试...")
    
    try:
        # 测试状态管理器
        from pyswap.models.state_manager import SwapState
        state = SwapState()
        state.initialize()
        print("✅ 状态管理器测试通过")
        
        # 测试模型类
        from pyswap.models.swap_model import SwapModel
        model = SwapModel()
        print("✅ 模型类创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 PySWAP构建脚本")
    print("=" * 50)
    
    # 检查依赖
    issues = check_dependencies()
    if issues:
        print("\n❌ 发现依赖问题:")
        for issue in issues:
            print(f"  - {issue}")
        print("\n请解决依赖问题后重试。")
        return False
    
    print("\n✅ 所有依赖检查通过")
    
    # 创建缺失文件
    create_missing_files()
    
    # 编译扩展
    if not build_extensions():
        print("\n❌ 编译失败，请检查错误信息")
        return False
    
    # 测试导入
    if not test_imports():
        print("\n⚠️  部分模块导入失败，这可能是正常的（如果某些模块尚未实现）")
    
    # 运行基本测试
    if run_basic_tests():
        print("\n🎉 构建和测试完成!")
    else:
        print("\n⚠️  构建完成，但基本测试失败")
    
    print("\n📖 下一步:")
    print("  1. 运行 'python -c \"import pyswap; print(pyswap.get_version_info())\"' 检查安装")
    print("  2. 运行 'pytest tests/' 执行完整测试套件")
    print("  3. 查看 'examples/' 目录中的使用示例")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
